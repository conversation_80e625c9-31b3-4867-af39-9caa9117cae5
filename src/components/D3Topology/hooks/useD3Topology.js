import { onBeforeUnmount, onMounted, ref, watch } from "vue";
import * as d3 from "d3";
import { useTopology } from "@/hooks/useTopology";

// 预加载所有图标
const iconModules = import.meta.glob("/src/assets/images/*_icon.png", { eager: true });

/**
 * D3拓扑图核心逻辑Hook
 * @param {Object} props - 组件属性
 * @param {Function} emit - 事件发射器
 * @param {Object} options - 额外选项
 * @returns {Object} - 返回拓扑图相关的状态和方法
 */
export function useD3Topology(props, emit, options = {}) {
  const { t, locale, globalStore } = options;

  // 容器引用
  const container = ref(null);
  let svg = null;

  // 布局方向
  const currentDirection = ref(props.direction);

  // 存储当前的变换状态
  const currentTransform = ref(null);

  // 存储缩放行为和SVG引用
  const zoomBehaviorRef = ref(null);

  // 设备信息弹出相关状态
  const selectedDevice = ref({});
  const showDeviceInfo = ref(false);
  const popupX = ref(0);
  const popupY = ref(0);

  // 获取 getNodePortsFromParent 方法
  const { getNodePortsFromParent } = useTopology();

  // 刷新拓扑图
  const refreshTopology = () => {
    emit("refresh");
    initChart();
  };

  // 切换布局方向
  const toggleDirection = () => {
    currentDirection.value = currentDirection.value === "vertical" ? "horizontal" : "vertical";
    initChart();
  };

  // 恢复原始大小
  const resetZoom = () => {
    if (zoomBehaviorRef.value) {
      const { zoomBehavior, svg } = zoomBehaviorRef.value;

      // 创建一个新的变换，包含初始的居中偏移
      const initialTransform = d3.zoomIdentity.translate(
        props.width / 2, // 水平方向居中
        currentDirection.value === "vertical" ? props.height / 3 : props.height / 2 // 在纵向布局下将树的顶部移到更高的位置
      );

      // 应用这个变换
      svg.transition().duration(750).call(zoomBehavior.transform, initialTransform);
    }
  };

  // 关闭设备信息弹出
  const closeDeviceInfo = () => {
    showDeviceInfo.value = false;
  };

  // 点击容器外部时关闭设备信息
  const handleDocumentClick = event => {
    // 如果点击的不是节点元素，则关闭设备信息
    if (!event.target.closest(".node") && showDeviceInfo.value) {
      showDeviceInfo.value = false;
    }
  };

  // 转换数据格式
  const transformData = data => {
    if (!data) return { root: null, nodes: [], links: [] };

    const nodes = [];
    const links = [];
    const nodeMap = new Map();
    const visited = new Set(); // 添加访问记录防止循环

    // 递归处理节点
    const processNode = (node, parentId = null, depth = 0, path = []) => {
      if (!node) return null;

      // 创建节点ID
      const nodeId = node.id || node.deviceId || `node-${nodes.length}`;

      // 防止无限递归 - 检查路径中是否已经包含当前节点
      const nodeIdentifier = node.id || node.deviceId || node.name || JSON.stringify(node);
      if (path.includes(nodeIdentifier)) {
        console.warn(`检测到循环引用，跳过节点: ${node.name || nodeId}, 路径: ${path.join(' -> ')}`);
        return null;
      }

      // 检查是否已经处理过相同的节点组合
      const nodeKey = `${nodeId}-${parentId}-${depth}`;
      if (visited.has(nodeKey)) {
        console.warn(`检测到重复处理，跳过节点: ${node.name || nodeId}`);
        return null;
      }
      visited.add(nodeKey);

      // 添加当前节点到路径
      const currentPath = [...path, nodeIdentifier];

      // 创建新节点
      const newNode = {
        id: nodeId,
        name: node.name || "未命名节点",
        symbol: node.symbol || "",
        extra: node.extra || {},
        depth: depth,
        children: [],
        x0: 0,
        y0: 0,
        // 保留出口设备相关属性
        isExitDevice: !!node.isExitDevice,
        canParentBeExitDevice: node.canParentBeExitDevice
      };

      // 添加节点
      if (!nodeMap.has(nodeId)) {
        nodes.push(newNode);
        nodeMap.set(nodeId, newNode);
      }

      // 添加连线
      if (parentId) {
        const medium = node.extra?.medium || "CABLE";
        links.push({
          source: parentId,
          target: nodeId,
          medium: medium
        });
      }

      // 处理子节点
      if (node.children && Array.isArray(node.children)) {
        newNode.children = node.children
          .map(child => {
            return processNode(child, nodeId, depth + 1, currentPath);
          })
          .filter(Boolean);
      }

      return newNode;
    };

    // 处理根节点
    const root = processNode(data);

    return { root, nodes, links };
  };

  // 初始化图表
  const initChart = () => {
    console.log("Initializing chart...");
    if (!container.value || !props.data) {
      console.log("Container or data not available", container.value, props.data);
      return;
    }

    // 清除旧图表
    d3.select(container.value).selectAll("*").remove();
    console.log("Container cleared");

    // 确保宽度和高度始终为正值
    const width = Math.max(100, props.width || 100);
    const height = Math.max(100, props.height || 100);

    console.log(`创建SVG元素: 宽度=${width}, 高度=${height}`);

    // 创建SVG
    svg = d3
      .select(container.value)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .attr("viewBox", [0, 0, width, height])
      .attr("style", "max-width: 100%; height: auto;")
      .on("click", () => {
        // 点击空白区域关闭设备信息弹出
        showDeviceInfo.value = false;
      });

    // 创建主容器
    const g = svg.append("g");

    // 添加灰度滤镜定义，用于离线节点
    const defs = svg.append("defs");
    const grayscaleFilter = defs
      .append("filter")
      .attr("id", "grayscale")
      .attr("x", "0")
      .attr("y", "0")
      .attr("width", "100%")
      .attr("height", "100%");

    grayscaleFilter
      .append("feColorMatrix")
      .attr("type", "matrix")
      .attr("values", "0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0");

    // 添加缩放功能
    const zoomBehavior = d3
      .zoom()
      .scaleExtent([0.1, 4])
      .on("zoom", event => {
        g.attr("transform", event.transform);
        // 存储当前的变换状态，供恢复原始大小时使用
        currentTransform.value = event.transform;
      })
      .filter(event => {
        // 过滤掉节点上的点击事件，避免与节点点击冲突
        return !event.target.closest(".node");
      });

    // 创建初始变换，将图表居中
    // 在纵向布局下，我们需要考虑树的大小和形状
    const initialTransform = d3.zoomIdentity.translate(
      props.width / 2, // 水平方向居中
      currentDirection.value === "vertical" ? props.height / 3 : props.height / 2 // 在纵向布局下将树的顶部移到更高的位置
    );

    // 应用初始变换
    svg.call(zoomBehavior).call(zoomBehavior.transform, initialTransform);

    // 存储缩放行为和SVG引用，供恢复原始大小使用
    zoomBehaviorRef.value = { zoomBehavior, svg };

    // 处理数据
    const { root } = transformData(props.data);

    if (!root) return;

    // 创建树形布局
    const treeLayout = d3.tree();

    // 根据方向设置布局大小和节点间距
    if (currentDirection.value === "vertical") {
      // 纵向布局，水平方向留出足够空间，垂直方向调整以使树更好地适应视图
      treeLayout.size([props.width - 100, props.height * 0.8]);
      // 设置节点大小，确保节点不会重叠
      treeLayout.nodeSize([80, 100]);
    } else {
      // 横向布局时交换宽高，并增加节点间距
      treeLayout.size([props.height - 100, props.width - 200]);
      // 设置节点大小，避免节点重叠
      treeLayout.nodeSize([60, 120]);
      // 注意：在横向布局中，我们已经将主容器居中了，这里的size设置主要是为了确定节点分布的范围
    }

    // 计算节点位置
    const rootNode = d3.hierarchy(root);
    treeLayout(rootNode);

    // 为节点添加点击事件处理函数
    const handleNodeClick = (event, d) => {
      event.stopPropagation();
      event.preventDefault(); // 阻止默认行为

      // 如果是internet节点，不显示设备信息弹窗
      if (d.data.name === "internet") {
        // 只触发节点点击事件，不显示弹窗
        emit("nodeClick", d.data);
        return;
      }

      // 获取节点在屏幕上的坐标
      const nodeElement = event.currentTarget;
      const rect = nodeElement.getBoundingClientRect();

      // 设置弹出位置 - 使用节点中心坐标，而不是鼠标坐标
      popupX.value = rect.left + rect.width / 2; // 节点中心的X坐标
      popupY.value = rect.top + rect.height / 2; // 节点中心的Y坐标

      // 设置选中设备信息
      const extra = d.data.extra || {};

      // 获取父节点信息
      const parentNode = d.parent ? d.parent.data : null;

      // 计算节点是否可以设置为出口设备
      let canParentBeExitDevice = false;

      // 检查节点是否已经是出口设备
      const isExitDevice = !!d.data.isExitDevice;

      // 二级节点（直接连接到Internet的节点）不能设置为出口设备
      // 但如果已经是出口设备，则可以取消
      if (d.depth === 1) {
        // 二级节点不能设置为出口设备，但如果已经是出口设备，则保留该状态
        canParentBeExitDevice = false;
      }
      // 从三级节点开始判断（d.depth >= 2，因为根节点depth=0，一级节点depth=1）
      else if (d.depth >= 2) {
        // 动态获取端口信息
        const { sport, dport } = getNodePortsFromParent(d.data);
        const sportLower = (sport || "").toLowerCase();
        const dportLower = (dport || "").toLowerCase();
        const sportType = getPortType(sportLower);
        const dportType = getPortType(dportLower);
        if (!sport || !dport) {
          console.log(`节点 ${d.data.name} 的端口信息不完整，sport=${sport}, dport=${dport}`);
          canParentBeExitDevice = false;
        } else {
          console.log(`节点 ${d.data.name} 的端口类型: sport=${sportLower}(${sportType}), dport=${dportLower}(${dportType})`);
          canParentBeExitDevice = sportType === dportType;
          if (parentNode && parentNode.canParentBeExitDevice === false) {
            canParentBeExitDevice = false;
          }
        }
      }

      selectedDevice.value = {
        ...d.data,
        // 确保关键字段存在，优先使用extra中的字段
        name: extra.deviceName || d.data.name || "",
        deviceName: extra.deviceName || d.data.deviceName || "",
        deviceModel: extra.deviceModel || d.data.deviceModel || "",
        deviceType: extra.deviceType || d.data.deviceType || "",
        // 优先使用extra.deviceId作为设备ID
        deviceId: extra.deviceId || d.data.deviceId || "",
        // 添加SN序列号字段
        sn: extra.sn || d.data.sn || "",
        ipaddr: extra.ipaddr || d.data.ipaddr || "",
        mac: extra.mac || extra.macaddr || d.data.mac || d.data.macaddr || "",
        macaddr: extra.macaddr || d.data.macaddr || "",
        // 添加父节点信息和出口设备标志
        parentNode: parentNode,
        canParentBeExitDevice: canParentBeExitDevice,
        isExitDevice: isExitDevice,
        extra: extra
      };

      // 显示弹出框
      showDeviceInfo.value = true;

      // 触发节点点击事件
      emit("nodeClick", d.data);
    };

    // 创建连线
    g.append("g")
      .attr("fill", "none")
      .attr("stroke", () => {
        // 所有连接线使用相同的颜色
        return "#409EFF";
      })
      .attr("stroke-opacity", 0.8)
      .selectAll("path")
      .data(rootNode.links())
      .join("path")
      .attr("stroke-width", 2) // 增加线宽
      .attr("stroke", () => {
        // 所有连接线使用相同的颜色
        return "#409EFF";
      })
      .attr("stroke-dasharray", d => {
        const medium = d.source.data.extra?.medium || d.target.data.extra?.medium || "CABLE";
        return medium === "RADIO" ? "5,5" : null;
      })
      .attr("d", d => {
        const iconRadius = 20;
        if (currentDirection.value === "vertical") {
          // 纵向布局：线从下方出发到上方结束
          const sx = d.source.x;
          const sy = d.source.y + iconRadius;
          const tx = d.target.x;
          const ty = d.target.y - iconRadius;
          const midY = (sy + ty) / 2;
          return `M${sx},${sy} L${sx},${midY} L${tx},${midY} L${tx},${ty}`;
        } else {
          // 横向布局：线从右侧出发到左侧结束
          const sx = d.source.y + iconRadius;
          const sy = d.source.x;
          const tx = d.target.y - iconRadius;
          const ty = d.target.x;
          const midX = (sx + tx) / 2;
          return `M${sx},${sy} L${midX},${sy} L${midX},${ty} L${tx},${ty}`;
        }
      });

    // 创建节点组 - 修改点击区域
    const node = g
      .append("g")
      .selectAll(".node")
      .data(rootNode.descendants())
      .join("g")
      .attr("class", "node")
      .attr("transform", d => {
        // 根据当前方向设置节点位置
        return currentDirection.value === "vertical" ? `translate(${d.x},${d.y})` : `translate(${d.y},${d.x})`;
      })
      .style("cursor", "pointer") // 确保鼠标指针变化
      .on("click", handleNodeClick); // 为节点组添加点击事件

    // 添加节点图标 - 增加容错处理
    node
      .append("image")
      .attr("xlink:href", d => {
        let url = d.data.symbol || "";
        if (!url) {
          console.warn("No symbol found for node:", d.data);
          return ""; // 返回空字符串避免错误请求
        }

        // 采用最简单的方法处理图标URL
        const isProd = import.meta.env.PROD;
        const publicPath = isProd ? "/" : "/";

        try {
          // 如果是image://前缀，先删除前缀
          if (url.startsWith("image://")) {
            const isGray = url.startsWith("image://gray:");
            url = isGray ? url.substring(12) : url.substring(7);
          }

          // 如果是相对路径，确保它是绝对路径
          if (!url.startsWith("http") && !url.startsWith("/") && !url.startsWith("data:")) {
            url = "/" + url;
          }

          // 提取文件名
          const filename = url.split("/").pop();

          // 根据文件名判断图标类型
          if (filename && filename.includes("_icon")) {
            // 处理不同环境下的路径
            if (isProd) {
              // 生产环境下使用 /hicloudiot/assets/png/ 路径
              url = `${publicPath}assets/png/${filename}`;
            } else {
              // 开发环境下使用 Vite 的资源导入
              const iconPath = `/src/assets/images/${filename}`;
              if (iconModules[iconPath]) {
                url = iconModules[iconPath].default;
              } else {
                console.error(`Icon not found: ${filename}`);
                url = "";
              }
            }
          } else if (isProd && url.includes("/assets/")) {
            // 其他资源在生产环境下的处理
            const assetsIndex = url.indexOf("/assets/");
            const assetsPath = url.substring(assetsIndex);
            const cleanAssetsPath = assetsPath.startsWith("/") ? assetsPath.substring(1) : assetsPath;
            url = publicPath + cleanAssetsPath;
          }
        } catch (error) {
          console.error("Error processing URL:", error, url);
        }

        // 打印调试信息 - 对所有URL进行调试
        console.log(
          `URL Debug [${d.data.name || "unknown"}]: Original=${d.data.symbol}, Processed=${url}, Env=${isProd ? "Prod" : "Dev"}`
        );

        // 测试URL是否可访问
        if (!url.startsWith("data:")) {
          fetch(url, { method: "HEAD" })
            .then(response => {
              if (response.ok) {
                console.log(`URL ${url} is accessible`);
              } else {
                console.error(`URL ${url} returned status ${response.status}`);
              }
            })
            .catch(error => {
              console.error(`URL ${url} is not accessible:`, error.message);
            });
        }

        return url;
      })
      .attr("x", -20)
      .attr("y", -20)
      .attr("width", 40)
      .attr("height", 40)
      .style("opacity", d => {
        // 离线节点降低透明度
        return d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1 ? 0.6 : 1;
      })
      .attr("filter", d => {
        // 离线节点应用灰度滤镜
        return d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1 ? "url(#grayscale)" : null;
      })
      .on("click", handleNodeClick) // 添加点击事件到图标
      .on("error", function (event, d) {
        // 图片加载失败处理
        d3.select(this).remove();
        d3.select(this.parentNode)
          .append("circle")
          .attr("r", 20)
          .attr("fill", d.data.extra?.offline === 1 || d.data.status === 1 ? "#999" : "#ccc") // 离线节点使用更深的灰色
          .style("opacity", d.data.extra?.offline === 1 || d.data.status === 1 ? 0.6 : 1) // 离线节点降低透明度
          .on("click", function (event) {
            // 获取父节点的数据
            const parentNode = d3.select(this.parentNode).datum();
            handleNodeClick(event, parentNode);
          });

        // 如果是离线节点，添加一个斜线标记
        if (d.data.extra?.offline === 1 || d.data.status === 1) {
          d3.select(this.parentNode)
            .append("line")
            .attr("x1", -15)
            .attr("y1", -15)
            .attr("x2", 15)
            .attr("y2", 15)
            .attr("stroke", "#ff0000")
            .attr("stroke-width", 2);
        }
      });

    // 添加上方标签（上联口和下联口）
    node
      .filter(d => {
        // 动态判断是否有端口信息
        const { sport, dport } = getNodePortsFromParent(d.data);
        return sport || dport;
      })
      .append("g")
      .attr("transform", () => {
        return currentDirection.value === "vertical" ? "translate(0, -40)" : "translate(-40, 0)";
      })
      .attr("pointer-events", "none")
      .each(function (d) {
        // 动态获取端口信息
        const { sport, dport } = getNodePortsFromParent(d.data);
        let content = [];
        content.push(`${t("topology.uplink")}: ${sport ? sport : "--"}`);
        content.push(`${t("topology.downlink")}: ${dport ? dport : "--"}`);
        const labelGroup = d3.select(this);
        const lineHeight = 14;
        const text = labelGroup
          .append("text")
          .attr("text-anchor", "middle")
          .attr("font-size", "9px")
          .attr("fill", d => {
            return d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1
              ? "#999"
              : globalStore.isDark
                ? "#fff"
                : "#333";
          })
          .attr("font-weight", "normal")
          .attr("stroke", globalStore.isDark ? "rgba(0,0,0,0.5)" : "none")
          .attr("stroke-width", globalStore.isDark ? "0.3" : "0")
          .style("opacity", d => {
            return d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1 ? 0.8 : 1;
          });
        content.forEach((line, i) => {
          text
            .append("tspan")
            .attr("x", 0)
            .attr("dy", i === 0 ? 0 : lineHeight)
            .text(line);
        });
      });

    // 添加下方标签（节点名称）
    node
      .append("text")
      .attr("dy", () => (currentDirection.value === "vertical" ? 35 : 0)) // 纵向时在下方，横向时在右侧
      .attr("dx", () => (currentDirection.value === "vertical" ? 0 : 35)) // 纵向时不偏移，横向时向右偏移
      .attr("text-anchor", "middle")
      .attr("font-size", "9px")
      .attr("fill", d => {
        // 离线节点使用灰色文字，其他节点根据模式调整颜色
        return d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1
          ? "#999"
          : globalStore.isDark
            ? "#fff"
            : "#333";
      })
      .attr("stroke", globalStore.isDark ? "rgba(0,0,0,0.5)" : "none") // 暗黑模式下添加细微描边增强可读性
      .attr("stroke-width", globalStore.isDark ? "0.3" : "0") // 描边宽度
      .style("opacity", d => {
        // 离线节点降低透明度
        return d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1 ? 0.8 : 1;
      })
      .text(d => {
        const name = d.data.name;
        return name.includes(".") ? name.split(".").pop() : name;
      })
      .on("click", handleNodeClick); // 添加点击事件到文本标签

    // 为离线节点添加标记
    node
      .filter(d => d.data.extra?.offline === 1 || d.data.extra?.status === 1 || d.data.status === 1)
      .append("circle")
      .attr("r", 5)
      .attr("cx", 15)
      .attr("cy", -15)
      .attr("fill", "#ff0000")
      .attr("stroke", "#ffffff")
      .attr("stroke-width", 1);
  };

  // 组件挂载时添加事件监听
  onMounted(() => {
    document.addEventListener("click", handleDocumentClick);
  });

  // 组件卸载时移除事件监听
  onBeforeUnmount(() => {
    document.removeEventListener("click", handleDocumentClick);
  });

  // 监听数据变化
  watch(
    [() => props.data, () => locale.value, () => globalStore.isDark],
    () => {
      console.log("Data, locale or theme changed, refreshing chart...");
      // 当数据变化时，关闭设备信息弹窗
      showDeviceInfo.value = false;
      selectedDevice.value = {};
      initChart();
    },
    { deep: true, immediate: true }
  );

  // 监听布局方向变化
  watch(
    () => props.direction,
    newDirection => {
      currentDirection.value = newDirection;
      initChart();
    }
  );

  // 监听窗口大小变化
  const handleResize = () => {
    if (svg) {
      // 确保宽度和高度始终为正值
      const width = Math.max(100, props.width || 100);
      const height = Math.max(100, props.height || 100);

      console.log(`窗口大小变化: 宽度=${width}, 高度=${height}`);

      // 更新SVG元素的尺寸
      svg.attr("width", width).attr("height", height).attr("viewBox", [0, 0, width, height]);

      // 重新绘制图表以适应新尺寸
      // 使用setTimeout确保在下一个事件循环中调用，避免多次调用
      setTimeout(() => {
        initChart();
      }, 0);
    }
  };

  watch([() => props.width, () => props.height], handleResize);

  /**
   * 获取端口类型，分为LAN、WAN、RA三大类
   * @param {string} port 端口名称
   * @returns {string} 端口类型
   */
  const getPortType = port => {
    if (!port) return "";

    // 转换为小写进行比较
    const portLower = port.toLowerCase();

    // WAN类型端口
    if (portLower.startsWith("wan")) {
      return "WAN";
    }

    // RA类型端口
    if (portLower === "racli" || /^ra\d+$/i.test(portLower)) {
      return "RA";
    }

    // LAN类型端口，包括lan、ge、port、fe等
    if (portLower.startsWith("lan") || portLower.startsWith("ge") || portLower.startsWith("port") || portLower.startsWith("fe")) {
      return "LAN";
    }

    // 默认为LAN类型
    return "LAN";
  };

  return {
    container,
    currentDirection,
    selectedDevice,
    showDeviceInfo,
    popupX,
    popupY,
    refreshTopology,
    toggleDirection,
    resetZoom,
    closeDeviceInfo,
    initChart
  };
}
