<template>
  <div class="login-container flx-center">
    <div class="login-box">
      <div class="login-actions">
        <SwitchDark class="dark-switch" />
        <SwitchLanguage class="language-switch" />
      </div>
      <div class="login-left">
        <img class="login-left-img" src="@/assets/images/login_left6.png" alt="login" />
      </div>
      <div class="login-form login-card">
        <div class="login-logo">
          <img class="login-icon" src="@/assets/images/logo.png" alt="" />
          <h2 class="logo-text">
            <div ref="chart" class="chart-container"></div>
          </h2>
        </div>
        <LoginForm />
      </div>
    </div>
  </div>
  <div class="beian-wrapper">
    <div class="footer-main-row">
      <div class="copyright">{{ $t("footer.copyright") }}</div>
      <div class="beian-info">
        <a class="beian-link" href="https://beian.miit.gov.cn" target="_blank" :title="$t('footer.beianLink')">
          <span class="icon">🏛️</span>{{ $t("footer.beianNumber") }}
        </a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="login">
import LoginForm from "./components/LoginForm.vue";
import SwitchDark from "@/components/SwitchDark/index.vue";
import SwitchLanguage from "@/components/SwitchLanguage/index.vue";
import * as echarts from "echarts";
import { onMounted, ref } from "vue";

const chart = ref<HTMLDivElement | null>(null);

onMounted(() => {
  if (chart.value) {
    const myChart = echarts.init(chart.value);
    const option = {
      graphic: {
        elements: [
          {
            type: "text",
            left: "center",
            top: "center",
            style: {
              text: "CloudIot",
              fontSize: 57,
              fontWeight: "bold",
              lineDash: [0, 200],
              lineDashOffset: 0,
              fill: "transparent",
              stroke: "#6DA9E8",
              lineWidth: 1
            },
            keyframeAnimation: {
              duration: 3000,
              loop: true,
              keyframes: [
                {
                  percent: 0.7,
                  style: {
                    fill: "transparent",
                    lineDashOffset: 200,
                    lineDash: [200, 0]
                  }
                },
                {
                  percent: 0.9,
                  style: {
                    fill: "transparent"
                  }
                },
                {
                  percent: 0.9,
                  style: {
                    fill: "#387DDE"
                  }
                },
                {
                  percent: 1,
                  style: {
                    fill: "#387DDE"
                  }
                }
              ]
            }
          }
        ]
      }
    };
    myChart.setOption(option);
  }
});
</script>

<style scoped lang="scss">
@import "./index";
.login-box {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 96.5%;
  height: 94%;
  padding: 0 50px;
  margin-bottom: 18px;
  background-color: rgb(255 255 255 / 30%);
  backdrop-filter: blur(10px);
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 18px;
  box-shadow: 0 8px 32px rgb(0 0 0 / 10%);
  transition:
    box-shadow 0.3s,
    border-radius 0.3s;
}
.login-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-width: 320px;
  max-width: 400px;
  padding: 38px 32px 32px;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgb(64 158 255 / 10%);
  transition:
    box-shadow 0.3s,
    border-radius 0.3s;
  .login-logo {
    margin-bottom: 32px;
  }
}

// 移动端优化
@media screen and (width <= 768px) {
  .login-container {
    padding: 20px 12px;
  }
  .login-box {
    box-sizing: border-box;
    flex-direction: column;
    width: 100%;
    min-width: 0;
    height: auto;
    padding: 20px;
    margin-bottom: 15px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgb(64 158 255 / 12%);
  }
  .login-left {
    display: none; // 隐藏左侧装饰图片
  }
  .login-card {
    box-sizing: border-box;
    width: 100%;
    min-width: 0;
    max-width: none;
    padding: 24px 20px 20px;
    margin: 0;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgb(64 158 255 / 10%);
    .login-logo {
      margin-bottom: 20px;
      .login-icon {
        width: 48px;
        height: 48px;
      }
      .chart-container {
        height: 40px;
        margin-top: 8px;
      }
    }
  }
}

@media screen and (width <= 480px) {
  .login-container {
    padding: 16px 8px;
  }
  .login-box {
    padding: 12px;
    margin-bottom: 12px;
    border-radius: 14px;
  }
  .login-card {
    box-sizing: border-box;
    padding: 20px 16px 16px;
    margin: 0;
    border-radius: 14px;
    box-shadow: 0 1px 8px rgb(64 158 255 / 8%);
    .login-logo {
      margin-bottom: 16px;
      .login-icon {
        width: 42px;
        height: 42px;
      }
      .chart-container {
        height: 36px;
        margin-top: 6px;
      }
    }
  }
}

@media screen and (width <= 375px) {
  .login-container {
    padding: 12px 6px;
  }
  .login-box {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 12px;
  }
  .login-card {
    box-sizing: border-box;
    padding: 16px 12px 12px;
    margin: 0;
    border-radius: 12px;
    .login-logo {
      margin-bottom: 12px;
      .login-icon {
        width: 38px;
        height: 38px;
      }
      .chart-container {
        height: 32px;
        margin-top: 4px;
      }
    }
  }
}
.beian-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
  width: 100vw;
  padding: 8px 12px 4px;
  background: linear-gradient(180deg, rgb(255 255 255 / 0%) 0%, rgb(255 255 255 / 95%) 30%, rgb(255 255 255 / 98%) 100%);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgb(255 255 255 / 20%);
  transition: all 0.3s ease;
  .footer-main-row {
    display: flex;
    gap: 18px;
    align-items: center;
    justify-content: center;
    width: 100%;
    font-size: 13px;
    .copyright {
      overflow: hidden;
      font-size: 13px;
      font-weight: 400;
      color: #888888;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .beian-info {
      display: flex;
      align-items: center;
      justify-content: center;
      .beian-link {
        display: inline-flex;
        gap: 4px;
        align-items: center;
        padding: 2px 10px 2px 7px;
        font-size: 12px;
        color: #4b73c9;
        text-decoration: none;
        letter-spacing: 0.2px;
        background: rgb(64 158 255 / 8%);
        border-radius: 12px;
        box-shadow: 0 1px 4px rgb(64 158 255 / 6%);
        opacity: 0.92;
        transition: all 0.22s cubic-bezier(0.4, 0, 0.2, 1);
        .icon {
          margin-right: 2px;
          font-size: 14px;
          color: #409eff;
          opacity: 0.85;
          transition: all 0.22s cubic-bezier(0.4, 0, 0.2, 1);
        }
        &:hover {
          color: #ffffff;
          background: linear-gradient(90deg, #409eff 60%, #79bbff 100%);
          box-shadow: 0 2px 8px rgb(64 158 255 / 18%);
          opacity: 1;
          transform: translateY(-1px) scale(1.04);
          .icon {
            color: #ffffff;
            opacity: 1;
            transform: scale(1.12);
          }
        }
      }
    }
  }

  @media screen and (width <= 480px) {
    padding: 3px 4px 2px;
    .footer-main-row {
      flex-direction: column;
      gap: 2px;
      font-size: 11px;
      .copyright,
      .beian-info {
        justify-content: center;
      }
      .beian-link {
        padding: 2px 8px 2px 6px;
        font-size: 11px;
        border-radius: 10px;
        .icon {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
