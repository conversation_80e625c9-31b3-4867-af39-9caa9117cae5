<template>
  <div class="footer">
    <div class="footer-content">
      <div class="footer-main-row">
        <div class="copyright">
          {{ $t("footer.copyright") }}
        </div>
        <div class="beian-info">
          <a
            class="beian-link"
            href="https://beian.miit.gov.cn"
            target="_blank"
            rel="noopener noreferrer"
            :title="$t('footer.beianLink')"
          >
            <i class="iconfont icon-beian"></i>
            {{ $t("footer.beianNumber") }}
          </a>
        </div>
        <div class="footer-version">
          <i class="iconfont icon-tag"></i>
          <span>v1.0.0 Beta</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页尾组件不需要引入useI18n，因为模板中直接使用了$t()
</script>

<style scoped lang="scss">
.footer {
  min-height: 32px;
  padding: 6px 12px;
  background: linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-bg-color-page) 100%);
  border-top: 1px solid var(--el-border-color-light);
  box-shadow: 0 -2px 8px rgb(0 0 0 / 4%);
  transition: all 0.3s ease;
  .footer-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 1400px;
    min-height: 28px;
    margin: 0 auto;
  }
  .footer-main-row {
    display: flex;
    gap: 18px;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-width: 0;
    font-size: 13px;
    .copyright {
      overflow: hidden;
      font-size: 13px;
      font-weight: 400;
      color: var(--el-text-color-secondary);
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .beian-info {
      display: flex;
      align-items: center;
      justify-content: center;
      .beian-link {
        display: inline-flex;
        gap: 4px;
        align-items: center;
        padding: 2px 10px 2px 7px;
        font-size: 12px;
        color: #4b73c9;
        text-decoration: none;
        letter-spacing: 0.2px;
        background: rgb(64 158 255 / 8%);
        border-radius: 12px;
        box-shadow: 0 1px 4px rgb(64 158 255 / 6%);
        opacity: 0.92;
        transition: all 0.22s cubic-bezier(0.4, 0, 0.2, 1);
        .iconfont {
          margin-right: 2px;
          font-size: 14px;
          color: #409eff;
          opacity: 0.85;
          transition: all 0.22s cubic-bezier(0.4, 0, 0.2, 1);
        }
        &:hover {
          color: #ffffff;
          background: linear-gradient(90deg, #409eff 60%, #79bbff 100%);
          box-shadow: 0 2px 8px rgb(64 158 255 / 18%);
          opacity: 1;
          transform: translateY(-1px) scale(1.04);
          .iconfont {
            color: #ffffff;
            opacity: 1;
            transform: scale(1.12);
          }
        }
      }
    }
  }
  .footer-version {
    display: flex;
    gap: 4px;
    align-items: center;
    padding: 2px 12px;
    font-size: 13px;
    font-weight: 500;
    color: #409eff;
    background: linear-gradient(90deg, #e0eaff 0%, #f5faff 100%);
    border-radius: 12px;
    box-shadow: 0 1px 4px rgb(64 158 255 / 8%);
    .iconfont {
      font-size: 15px;
      color: #409eff;
      opacity: 0.85;
    }
  }

  // 移动端自动变为上下排列
  @media screen and (width <= 480px) {
    min-height: 26px;
    padding: 3px 4px;
    .footer-content {
      align-items: center;
      justify-content: center;
      width: 100%;
      min-width: 0;
    }
    .footer-main-row {
      flex-direction: column;
      gap: 2px;
      align-items: center;
      justify-content: center;
      width: 100%;
      font-size: 11px;
      .copyright,
      .beian-info {
        justify-content: center;
        width: 100%;
        overflow: visible;
        text-align: center;
        word-break: break-all;
        white-space: normal;
      }
      .beian-link {
        padding: 2px 8px 2px 6px;
        font-size: 11px;
        border-radius: 10px;
        .iconfont {
          font-size: 12px;
        }
      }
    }
    .footer-version {
      padding: 2px 8px;
      font-size: 11px;
      border-radius: 10px;
      .iconfont {
        font-size: 13px;
      }
    }
  }

  @media screen and (width <= 350px) {
    .footer-main-row {
      gap: 1px;
      font-size: 10px;
    }
    .beian-link {
      padding: 2px 4px;
      font-size: 10px;
    }
  }

  // 暗色主题适配
  .dark & {
    background: linear-gradient(135deg, var(--el-bg-color) 0%, rgb(0 0 0 / 10%) 100%);
    border-top-color: var(--el-border-color);
    box-shadow: 0 -2px 8px rgb(0 0 0 / 15%);
    .beian-link {
      color: #90baff;
      background: rgb(64 158 255 / 16%);
      .iconfont {
        color: #90baff;
      }
      &:hover {
        color: #ffffff;
        background: linear-gradient(90deg, #337ecc 60%, #409eff 100%);
        .iconfont {
          color: #ffffff;
        }
      }
    }
  }
}
</style>
