import { ref, reactive, nextTick } from "vue";
import { TopologyData, DrawerProps, TopologyNode } from "@/api/interface/topology";
import { ElMessage } from "element-plus";
import {
  getTopologyData,
  saveDeviceName,
  deleteBridgeClient,
  getDeviceTopology,
  saveGroupExit,
  deleteExitByDeviceId,
  getExitByGroupId
} from "@/api/modules/topology";
import { useI18n } from "vue-i18n";

// 存储所有设备的MAC地址集合，用于过滤重复设备
const allDeviceMacAddresses = new Set();

// 存储所有peer设备的MAC地址集合，用于过滤子设备
const peerMacAddresses = new Set();

// 存储所有设备的节点引用，按MAC地址索引
const deviceNodesByMac = new Map();

// 存储出口设备ID的集合
const exitDeviceSet = new Set();

// 导入图标资源
import bridgeIcon_onLine from "@/assets/images/bridge_list_icon.png";
import otherDeviceIcon_online from "@/assets/images/device_other_online_icon.png";
import internetIcon from "@/assets/images/internet_icon.png";
import router_icon from "@/assets/images/router_icon.png";
import switch_icon from "@/assets/images/switch_icon.png";
import ac_icon from "@/assets/images/ac_icon.png";
import ap_icon from "@/assets/images/ap_icon.png";
import repeater_icon from "@/assets/images/zhongji_icon.png";

/**
 * 拓扑图数据处理 Hook
 */
export function useTopology() {
  // 获取国际化函数
  const { t } = useI18n();

  // 拓扑图数据
  const topologyData = reactive<TopologyData>({
    name: "internet",
    symbol: "image://" + internetIcon,
    children: []
  });

  // 抽屉组件状态
  const drawerVisible = ref(false);
  const drawerProps = ref<DrawerProps>({
    isView: false,
    title: "",
    row: {}
  });

  // 点击节点属性
  const clickNodeProps = ref<DrawerProps>({
    isView: false,
    title: "",
    row: {}
  });

  // 编辑状态
  const editName = ref(false);
  const deviceNameChanged = ref(false);
  const bridgeClientDrawerVisible = ref(false);

  /**
   * 获取设备图标
   * @param type 设备类型
   * @param online 在线状态
   * @returns 设备图标URL
   */
  const getDeviceIcon = (type: string, online: number): string => {
    // 获取基础图标
    let iconUrl = "";
    if (type === "route") {
      iconUrl = router_icon;
    } else if (type === "switch") {
      iconUrl = switch_icon;
    } else if (type === "ac") {
      iconUrl = ac_icon;
    } else if (type === "ap") {
      iconUrl = ap_icon;
    } else if (type === "bridge") {
      iconUrl = bridgeIcon_onLine;
    } else if (type === "repeater") {
      iconUrl = repeater_icon;
    } else {
      iconUrl = otherDeviceIcon_online;
    }

    // 根据在线状态返回图标URL
    if (online === 0) {
      // 在线状态，返回正常图标
      return "image://" + iconUrl;
    } else {
      // 离线状态，返回灰色图标
      // 使用字符串形式返回，但添加特殊标记以便在其他地方处理
      return "image://gray:" + iconUrl;
    }
  };

  // const topologyItemArray = ref<topologyItem>(null);
  /**
   * 创建设备节点对象
   * @param device 设备信息
   * @param status 设备在线状态
   * @returns 设备节点对象
   */
  const createDeviceNode = (device: any, status: number) => {
    const deviceIcon = getDeviceIcon(device.deviceType, status === 0 ? 0 : 1);
    return {
      name: device.deviceName || device.name || device.deviceModel || t("topology.unknown"),
      symbol: deviceIcon,
      children: [],
      extra: { ...device }
    };
  };

  /**
   * 检查两个节点是否相同
   * @param node1 节点1
   * @param node2 节点2
   * @returns 是否相同
   */
  const isSameNode = (node1: any, node2: any): boolean => {
    if (!node1 || !node2) return false;
    const mac1 = node1.extra?.macaddr || node1.extra?.mac;
    const mac2 = node2.extra?.macaddr || node2.extra?.mac;
    if (!mac1 || !mac2) return false;
    return mac1.toLowerCase() === mac2.toLowerCase();
  };

  /**
   * 递归在树中查找节点
   * @param node 要查找的节点
   * @param tree 要检查的树
   * @returns 找到的节点或null
   */
  const findNodeInTree = (node: any, tree: any, path: any[] = [], parentNode: any = null, visited = new Set()): any => {
    if (!tree) return null;
    if (visited.has(tree)) return null;
    visited.add(tree);
    // 检查当前节点
    if (isSameNode(node, tree)) {
      return {
        node: tree,
        path: [...path, tree],
        parentNode: parentNode
      };
    }
    // 递归检查子节点
    if (tree.children && tree.children.length > 0) {
      for (let i = 0; i < tree.children.length; i++) {
        const child = tree.children[i];
        const result = findNodeInTree(node, child, [...path, tree], tree, visited);
        if (result) {
          result.indexInParent = i; // 记录节点在父节点children中的索引
          return result;
        }
      }
    }
    return null;
  };

  /**
   * 在拓扑图中查找节点
   * @param node 要查找的节点
   * @returns 找到的节点或null
   */
  const findNodeInTopology = (node: any): any => {
    const visited = new Set();
    // 检查根节点
    if (isSameNode(node, topologyData)) {
      return {
        node: topologyData,
        path: [topologyData],
        isRoot: true,
        level: 0
      };
    }
    // 检查所有子节点
    for (let i = 0; i < topologyData.children.length; i++) {
      const child = topologyData.children[i];
      const result = findNodeInTree(node, child, [topologyData], topologyData, visited);
      if (result) {
        result.isDirectChild = result.path.length === 2; // 如果路径长度为2，则是根节点的直接子节点
        result.indexInRoot = i; // 记录节点在根节点children中的索引
        result.level = result.path.length - 1; // 记录节点的层级，根节点为0级
        return result;
      }
    }
    return null;
  };

  // 工具函数：判断 parent 是否为 target 的祖先节点，防止环
  function isAncestor(target, parent, visited = new Set()) {
    if (!parent || !parent.children) return false;

    // 防止无限递归 - 如果已经访问过这个节点，直接返回false
    const parentKey = parent.id || parent.deviceId || parent.name || JSON.stringify(parent);
    if (visited.has(parentKey)) return false;
    visited.add(parentKey);

    if (parent.children.includes(target)) return true;
    return parent.children.some(child => isAncestor(target, child, visited));
  }

  /**
   * 合并两个节点的子节点
   * @param sourceNode 源节点
   * @param targetNode 目标节点
   */
  const mergeNodeChildren = (sourceNode: any, targetNode: any, visited = new Set()) => {
    if (!sourceNode || !targetNode) {
      console.error("mergeNodeChildren: sourceNode or targetNode is undefined", { sourceNode, targetNode });
      return;
    }
    if (visited.has(targetNode)) return; // 防止死循环
    visited.add(targetNode);
    // 检查源节点和目标节点是否有效
    if (!sourceNode || !targetNode) {
      console.error("mergeNodeChildren: sourceNode or targetNode is undefined", { sourceNode, targetNode });
      return;
    }

    // 目标节点已经在拓扑图中，可以查找其父节点
    const targetParentName = findParentNodeName(targetNode, visited);

    // 源节点可能还没有被添加到拓扑图中，所以不查找其父节点
    console.log(`mergeNodeChildren: 合并节点 ${sourceNode.name} 到 ${targetNode.name} (父节点: ${targetParentName})`);

    if (sourceNode.extra?.deviceName) {
      targetNode.name = sourceNode.extra.deviceName;
    }

    // 检查源节点和目标节点的MAC地址和序列号
    const sourceMac = sourceNode.extra?.macaddr || sourceNode.extra?.mac;
    const targetMac = targetNode.extra?.macaddr || targetNode.extra?.mac;
    const sourceSN = sourceNode.extra?.sn || sourceNode.sn;
    // 获取目标节点的SN，用于比较和判断
    const targetSN = targetNode.extra?.sn;
    const sourceDeviceId = sourceNode.extra?.deviceId;
    const targetDeviceId = targetNode.extra?.deviceId;

    // 如果源节点和目标节点的MAC地址不同，但deviceId相同，这可能是一个错误
    if (
      sourceMac &&
      targetMac &&
      sourceMac !== targetMac &&
      sourceDeviceId &&
      targetDeviceId &&
      sourceDeviceId === targetDeviceId
    ) {
      console.warn(`警告: 合并节点时发现MAC地址不同但deviceId相同: ${sourceMac} 和 ${targetMac}, deviceId=${sourceDeviceId}`);
      // 在这种情况下，我们不应该覆盖deviceId
      console.log(`  保留原有deviceId值，不进行覆盖`);
    } else {
      // 处理deviceId
      if (!targetNode.extra.deviceId && sourceNode.extra?.deviceId) {
        // 只有当目标节点没有deviceId时，才使用源节点的deviceId
        targetNode.extra.deviceId = sourceNode.extra.deviceId;
      }

      // 处理sn字段
      if (!targetSN && sourceSN) {
        // 只有当目标节点没有sn时，才使用源节点的sn
        console.log(`  目标节点没有SN，使用源节点的SN: ${sourceSN}`);
        targetNode.extra.sn = sourceSN;
      } else if (targetSN && sourceSN && targetSN !== sourceSN) {
        // 如果两个节点的SN不同，输出警告
        console.log(`  警告: 源节点和目标节点的SN不同: ${sourceSN} 和 ${targetSN}`);
      }
    }

    // 如果源节点没有子节点，直接返回
    if (!sourceNode.children || sourceNode.children.length === 0) {
      console.log(`  源节点没有子节点，无需合并`);
      return;
    }

    // 确保目标节点有children数组
    if (!targetNode.children) {
      targetNode.children = [];
    }

    console.log(`  源节点有 ${sourceNode.children.length} 个子节点需要合并`);

    // 遍历源节点的所有子节点
    for (const childNode of sourceNode.children) {
      // 防止环：禁止祖先节点被挂载为子节点
      if (isAncestor(targetNode, childNode)) {
        console.warn(`检测到环，禁止将祖先节点 ${targetNode.name} 挂载为 ${childNode.name} 的子节点`);
        continue;
      }
      // 在目标节点的子节点中查找相同的节点
      const existingChildIndex = targetNode.children.findIndex(item => isSameNode(childNode, item));

      if (existingChildIndex !== -1) {
        // 如果找到相同的子节点，递归合并它们的子节点
        console.log(`    子节点 ${childNode.name} 已存在，递归合并`);
        mergeNodeChildren(childNode, targetNode.children[existingChildIndex], visited);
      } else {
        // 如果没有找到相同的子节点，将子节点添加到目标节点
        console.log(`    添加新子节点 ${childNode.name} 到目标节点`);
        targetNode.children.push(childNode);
      }
    }

    console.log(`  合并完成，目标节点现有 ${targetNode.children.length} 个子节点`);
  };

  /**
   * 查找节点的父节点
   * @param node 要查找父节点的节点
   * @returns 父节点信息或null
   */
  /**
   * 查找节点的父节点名称
   * @param node 要查找父节点的节点
   * @param currentNode 当前节点，默认为根节点
   * @returns 父节点名称
   */
  const findParentNodeName = (node: any, currentNode: any = topologyData, visited = new Set()): string => {
    if (!currentNode || !currentNode.children) {
      return "";
    }
    if (visited.has(currentNode)) return "";
    visited.add(currentNode);
    for (const child of currentNode.children) {
      if (isSameNode(child, node)) {
        return currentNode.name || "无名称";
      }
      const result = findParentNodeName(node, child, visited);
      if (result !== "") {
        return result;
      }
    }
    return "";
  };

  /**
   * 移动节点到新的位置
   * @param nodeInfo 要移动的节点信息
   * @param targetNode 目标节点
   * @returns 是否移动成功
   */
  const moveNodeToTarget = (nodeInfo: any, targetNode: any) => {
    if (!nodeInfo || !targetNode) {
      console.error("moveNodeToTarget: nodeInfo or targetNode is undefined", { nodeInfo, targetNode });
      return false;
    }

    const { node, parentNode, indexInParent, isDirectChild, indexInRoot } = nodeInfo;

    // 查找节点的父节点
    const nodeParentName = parentNode ? parentNode.name : findParentNodeName(node);

    // 查找目标节点的父节点
    const targetParentName = findParentNodeName(targetNode);

    console.log(
      `moveNodeToTarget: 尝试移动节点 ${node.name} (父节点: ${nodeParentName}) 到目标节点 ${targetNode.name} (父节点: ${targetParentName})`
    );
    console.log(`  节点层级: ${nodeInfo.level}, 是否为根节点直接子节点: ${isDirectChild}`);

    // 确保目标节点有children属性
    if (!targetNode.children) {
      targetNode.children = [];
    }

    // 如果节点是根节点的直接子节点
    if (isDirectChild) {
      // 从根节点中移除该节点
      topologyData.children.splice(indexInRoot, 1);
      console.log(`  从根节点中移除节点 ${node.name}`);
    } else if (parentNode && typeof indexInParent === "number") {
      // 从父节点中移除该节点
      parentNode.children.splice(indexInParent, 1);
      console.log(`  从父节点 ${parentNode.name} 中移除节点 ${node.name}`);
    }

    //在这里替换正确的sport和dport
    if (targetNode?.extra?.system?.topology) {
      let { peer, subDev } = targetNode.extra?.system?.topology;
      // 在目标节点的extra中添加peer和subDev，用macaddr匹配
      let peerObj;
      let subDevObj;
      if (peer && subDev) {
        peerObj = peer.find((item: any) => item.macaddr === node.extra?.macaddr);
        subDevObj = subDev.find((item: any) => item.macaddr === node.extra?.macaddr);
      }
      if (peerObj) {
        // 注意：在父节点的peer/subDev数据中，
        // sport是父节点连接到子节点的端口，应该是子节点的dport
        // dport是子节点连接到父节点的端口，应该是子节点的sport
        node.extra.sport = peerObj.dport; // 父节点的dport是子节点的sport
        node.extra.dport = peerObj.sport; // 父节点的sport是子节点的dport
      }
      if (subDevObj) {
        // 同样需要交换sport和dport
        node.extra.sport = subDevObj.dport; // 父节点的dport是子节点的sport
        node.extra.dport = subDevObj.sport; // 父节点的sport是子节点的dport
      }
    }

    // 将节点添加到目标节点的子节点中
    targetNode.children.push(node);
    console.log(`  将节点 ${node.name} 添加到目标节点 ${targetNode.name} 的子节点中`);

    // 输出移动后的节点层级结构
    console.log("\n节点层级结构:");
    logNodeStructure(topologyData, 0);

    return true;
  };

  /**
   * 添加节点到拓扑图，全局去重并合并子节点
   * @param node 要添加的节点
   * @param targetArray 目标数组,上级节点的children数组，例如topologyData.children
   * @returns 是否添加成功（true：新增节点，false：合并到现有节点）
   */
  const addNodeWithDedupe = (node: any, targetArray: any) => {
    // 检查参数是否有效
    if (!node) {
      console.error("addNodeWithDedupe: node is undefined");
      return false;
    }

    // 检查 targetArray 是否为有效数组
    if (!targetArray.children || !Array.isArray(targetArray.children)) {
      console.error("addNodeWithDedupe: targetArray is not a valid array", targetArray);
      return false;
    }

    // 新增：判断当前 targetArray 是否为 compareLinkPriority 最优父节点
    // 收集所有父链路
    const allParentLinks: { link: any; parentNode: any }[] = [];
    if (node.extra && node.extra._allParentLinks && node.extra._allParentLinks.length > 0) {
      for (const l of node.extra._allParentLinks) {
        allParentLinks.push({ link: l.link, parentNode: l.parentNode || l.parent });
      }
    } else if (node.extra && node.extra.system && node.extra.system.topology) {
      const peers = node.extra.system.topology.peer || [];
      const subDevs = node.extra.system.topology.subDev || [];
      const allLinks = [...peers, ...subDevs];
      for (const link of allLinks) {
        const parentNode = targetArray; // 兜底用当前父节点
        allParentLinks.push({ link, parentNode });
      }
    }
    if (allParentLinks.length > 0) {
      let best = allParentLinks[0];
      for (let i = 1; i < allParentLinks.length; i++) {
        if (compareLinkPriority(allParentLinks[i].link, best.link) < 0) {
          best = allParentLinks[i];
        }
      }
      if (targetArray !== best.parentNode) {
        // 只保留链路信息，不挂载
        return false;
      }
    }

    console.log(`addNodeWithDedupe: 尝试添加节点 ${node.name} 到目标数组`);

    // 先在目标数组中查找是否有相同节点
    const existingNodeIndex = targetArray.children.findIndex(item => isSameNode(node, item));

    // 如果在目标数组中找到相同节点
    if (existingNodeIndex !== -1) {
      console.log(`  在目标数组中找到相同节点: ${targetArray.children[existingNodeIndex].name}`);
      // 如果在目标数组中找到相同节点，则合并子节点
      // 注意：这里的 targetArray[existingNodeIndex] 已经在拓扑图中，而 node 还没有被添加
      mergeNodeChildren(node, targetArray.children[existingNodeIndex]);
      return false;
    }

    // 记录MAC地址检查信息
    const macAddr = node.extra?.macaddr || node.extra?.mac;
    if (macAddr && macAddr.toUpperCase() === "90-E2-FF-00-08-0D") {
      console.log(`[特殊MAC检查] 尝试添加MAC地址为 ${macAddr} 的节点:`, {
        nodeName: node.name,
        deviceType: node.extra?.deviceType,
        deviceId: node.extra?.deviceId,
        isSubDev: node.extra?.subDev ? true : false,
        isPeer: node.extra?.isPeer ? true : false
      });
    }

    // 再检查节点是否已存在于整个拓扑图中的其他位置
    const existingNodeInfo = findNodeInTopology(node);
    if (existingNodeInfo) {
      const existingNode = existingNodeInfo.node;

      // 针对特定MAC地址的日志记录
      const existingMac = existingNode.extra?.macaddr || existingNode.extra?.mac;
      if (macAddr && existingMac && macAddr.toUpperCase() === "90-E2-FF-00-08-0D") {
        console.log(`[特殊MAC检查] 在拓扑图中找到相同MAC地址 ${macAddr} 的节点:`, {
          existingNode: {
            name: existingNode.name,
            deviceType: existingNode.extra?.deviceType,
            deviceId: existingNode.extra?.deviceId,
            isSubDev: existingNode.extra?.subDev ? true : false,
            isPeer: existingNode.extra?.isPeer ? true : false,
            parent: findParentNodeName(existingNode)
          }
        });
      }

      // 查找现有节点的父节点
      const existingParentName = findParentNodeName(existingNode);

      // 查找目标数组的父节点
      // const targetParentNode = findParentNodeByChildrenArray(targetArray);
      // const targetParentName = targetParentNode ? targetParentNode.name : "无父节点";
      //
      // console.log(
      //   `  在拓扑图中找到相同节点: ${existingNode.name} (父节点: ${existingParentName}), 层级: ${existingNodeInfo.level}`
      // );
      // console.log(`  目标位置父节点: ${targetParentName}`);

      // 判断已存在节点的父级节点是否是根节点是否需要移动节点
      // 如果现有节点是根节点的直接子节点，而目标数组不是根节点的children
      const isExistRootChildren = topologyData.name === existingParentName;

      if (isExistRootChildren) {
        // 如果现有节点是根节点的直接子节点，而目标不是根节点，则移动节点
        console.log(`  现有节点是根节点的直接子节点，需要移动到非根节点位置`);

        if (targetArray.name !== "internet") {
          moveNodeToTarget(existingNodeInfo, targetArray);
          return true;
        }
      }

      // 如果节点已经存在于拓扑图中，则合并子节点
      // 注意：这里的 existingNode 已经在拓扑图中，而 node 还没有被添加
      mergeNodeChildren(node, existingNode);
      return false;
    }
    // 如果当前节点node.extra.topologyType 不为空，则说明是查询的离线拓扑图
    if (node.extra && node.extra.topologyType) {
      // 如果节点的extra.topologyType不为空，则说明是查询的离线拓扑图,则将extra.deviceId置空，因为数据库中保存的deviceId标识的父级节点的deviceId是子级节点的deviceId
      node.extra.deviceId = null;
    }

    // 如果不存在重复，则添加新节点
    console.log(`  未找到相同节点，添加新节点: ${node.name}`);
    targetArray.children.push(node);

    return true;
  };

  /**
   * 检查设备的MAC地址是否已经存在于拓扑图中
   * @param macaddr MAC地址
   * @returns 是否已经存在
   */
  const isMacAddressExists = (macaddr: string): boolean => {
    if (!macaddr) return false;

    // 转换为小写以确保匹配
    const normalizedMac = macaddr.toLowerCase();
    return allDeviceMacAddresses.has(normalizedMac);
  };

  /**
   * 添加MAC地址到集合中
   * @param macaddr MAC地址
   * @param node 节点引用
   */
  const addMacAddress = (macaddr: string, node: any): void => {
    if (!macaddr) return;

    // 转换为小写以确保匹配
    const normalizedMac = macaddr.toLowerCase();
    allDeviceMacAddresses.add(normalizedMac);
    deviceNodesByMac.set(normalizedMac, node);
    console.log(`添加MAC地址到集合: ${macaddr}`);
  };

  /**
   * 删除重复的子设备
   * 递归遍历拓扑图，删除所有与peer设备MAC地址相同的子设备
   */
  const removeSubDevWithSameMacAsPeer = () => {
    // 收集所有peer设备的MAC地址
    const peerMacs = new Set();

    // 第一次遍历，收集所有peer设备的MAC地址
    const collectPeerMacs = (node: any, visited = new Set()) => {
      if (!node) return;
      if (visited.has(node)) return;
      visited.add(node);
      // 如果节点是peer设备，收集其MAC地址
      if (node.extra?.topologyType === "peer") {
        const mac = node.extra?.macaddr || node.extra?.mac;
        if (mac) {
          peerMacs.add(mac.toLowerCase());
          console.log(`收集peer设备MAC: ${mac}`);
        }
      }

      // 递归遍历子节点
      if (node.children && Array.isArray(node.children)) {
        node.children.forEach(child => collectPeerMacs(child, visited));
      }
    };

    // 第二次遍历，删除重复的子设备
    const removeSubDevs = (node: any, visited = new Set()) => {
      if (!node || !node.children) return;
      if (visited.has(node)) return;
      visited.add(node);
      // 过滤子节点，删除重复的子设备
      node.children = node.children.filter(child => {
        // 如果不是子设备，保留
        if (child.extra?.topologyType !== "subDev") {
          return true;
        }

        // 如果是子设备，检查MAC地址是否在peer设备中存在
        const mac = child.extra?.macaddr || child.extra?.mac;
        if (mac && peerMacs.has(mac.toLowerCase())) {
          console.log(`删除重复的子设备: ${child.name}, MAC: ${mac}`);
          return false; // 删除该子设备
        }

        return true; // 保留该子设备
      });

      // 递归处理子节点
      node.children.forEach(child => removeSubDevs(child, visited));
    };

    // 执行处理
    collectPeerMacs(topologyData);
    removeSubDevs(topologyData);

    console.log(`处理完成，共收集到 ${peerMacs.size} 个peer设备的MAC地址`);
  };

  /**
   * 生成拓扑图数据
   * @param topologyRes 设备数据
   * @param device 主设备信息
   * @param t 国际化函数
   */
  const generateData = async (topologyRes: any, device: any, t: any) => {
    console.log("topologyRes:", JSON.stringify(topologyRes));
    try {
      // 清空设备MAC地址集合，准备重新收集
      // allDeviceMacAddresses.clear();
      // deviceNodesByMac.clear();
      // // parentMap.clear();
      // deviceNodesByDeviceId.clear();

      // 处理空数据情况
      if (!topologyRes) {
        const deviceNode = createDeviceNode(device, device.status);
        addNodeWithDedupe(deviceNode, topologyData);
        console.error(t("topology.noTopologyData"));
        return null;
      }
      device.system = topologyRes.data?.system;

      // 处理拓扑数据
      if (topologyRes.data?.system === undefined) {
        // 处理超时设备
        const timeoutDeviceNode = createDeviceNode(device, device.status);
        addNodeWithDedupe(timeoutDeviceNode, topologyData);
      } else {
        // 清空 peer MAC 地址集合
        // peerMacAddresses.clear();

        // 先收集所有peer设备的MAC地址
        const allPeerDevices = topologyRes.data?.system?.topology?.peer || [];
        allPeerDevices.forEach((peer: any) => {
          const macaddr = peer.macaddr || peer.mac;
          if (macaddr) {
            console.log(`添加peer设备MAC地址到集合: ${macaddr}`);
            peerMacAddresses.add(macaddr.toLowerCase()); // 转换为小写以确保匹配
          }
        });
        console.log(`收集到 ${peerMacAddresses.size} 个peer设备的MAC地址`);

        // 处理peer设备
        allPeerDevices.forEach((peer: any) => {
          processDeviceRelation(peer, device, peer.offline || 0, false);
        });

        // 处理sub设备，过滤掉已经存在于peer数据中的设备
        const subDevices = topologyRes.data?.system?.topology?.subDev || [];
        subDevices.forEach((sub: any) => {
          const macaddr = sub.macaddr || sub.mac;
          if (macaddr && peerMacAddresses.has(macaddr.toLowerCase())) {
            console.log(`子设备 ${sub.name || ""} (MAC: ${macaddr}) 已经存在于peer数据中，跳过显示`);
            return; // 跳过这个子设备
          }
          processDeviceRelation(sub, device, sub.offline || 0, true);
        });

        // 修复：处理孤立设备（没有peer和subDev连接的设备）
        if (allPeerDevices.length === 0 && subDevices.length === 0) {
          console.log(`设备 ${device.deviceId} 没有peer和subDev连接，作为孤立设备添加到拓扑图中`);
          const isolatedDeviceNode = createDeviceNode(device, device.status);
          // 将设备的完整拓扑数据存储到节点的extra中，以便后续使用
          if (topologyRes.data?.system?.topology) {
            isolatedDeviceNode.extra.topology = topologyRes.data.system.topology;
          }
          addNodeWithDedupe(isolatedDeviceNode, topologyData);
        }
      }

      // 删除所有与peer设备MAC地址相同的子设备
      removeSubDevWithSameMacAsPeer();

      // 处理重复MAC地址的节点，按照规则合并信息
      mergeDuplicateMacNodes();

      console.log("拓扑图数据生成完成:", topologyData);

      // 构建父节点映射关系并设置端口信息
      buildParentMapAndLinkInfo(topologyData);

      // 输出拓扑图数据结构
      console.log("Topology Data:", JSON.stringify(topologyData));

      // 输出节点层级结构
      console.log("\n节点层级结构:");
      logNodeStructure(topologyData, 0);

      // 确保视图更新
      await nextTick();
      // 修正所有节点主父节点唯一挂载
      fixAllNodeParentByPriority(topologyData);
      return topologyData;
    } catch (error) {
      console.error(t("topology.loadFailed") + ":", error);
      return null;
    }
  };

  /**
   * 处理设备关系的通用函数
   * @param relatedDevice peer或subDev设备
   * @param mainDevice 主设备
   * @param itemStatus 状态
   * @param isSubDevice 是否为subDev设备
   * @returns 是否处理成功
   */
  const processDeviceRelation = (relatedDevice: any, mainDevice: any, itemStatus: number, isSubDevice: boolean): boolean => {
    // 检查参数是否有效
    if (!relatedDevice || !mainDevice) {
      console.error("processDeviceRelation: relatedDevice or mainDevice is undefined", { relatedDevice, mainDevice });
      return false;
    }

    // 获取设备的MAC地址
    const macaddr = relatedDevice.macaddr || relatedDevice.mac;

    // 如果是子设备，先检查MAC地址是否已经存在
    if (isSubDevice && macaddr) {
      if (isMacAddressExists(macaddr)) {
        console.log(`子设备 ${relatedDevice.name || ""} (MAC: ${macaddr}) 已经存在于拓扑图中，跳过显示`);
        return false; // 跳过这个子设备
      }
    }

    console.log(
      `处理设备关系: ${mainDevice.deviceName || mainDevice.name} <-> ${relatedDevice.deviceName || relatedDevice.name}`
    );
    console.log(`  主设备状态: ${mainDevice.status}, 关联设备状态: ${relatedDevice.status}, 是否为子设备: ${isSubDevice}`);
    console.log(`  关联设备端口: sport=${relatedDevice.sport}, dport=${relatedDevice.dport}`);

    // 标记设备类型，便于后续过滤
    if (isSubDevice) {
      relatedDevice.topologyType = "subDev";
    } else {
      relatedDevice.topologyType = "peer";
    }

    // 对于peer或subDev设备，应使用sn字段作为设备自身的序列号，deviceId字段指向父级设备
    if (relatedDevice.sn) {
      console.log(`  关联设备有sn字段: ${relatedDevice.sn}，使用它作为设备自身的序列号`);
      // 备份原始的deviceId，它指向父级设备
      relatedDevice.parentDeviceId = relatedDevice.deviceId;
      // 使用sn作为设备自身的序列号
      relatedDevice.deviceId = relatedDevice.sn;
    }

    // 如果设备有MAC地址，将其添加到集合中
    if (macaddr) {
      addMacAddress(macaddr, relatedDevice);
    }

    // 构建主设备节点
    const mainNode = createDeviceNode(mainDevice, itemStatus);

    // 构建关联设备节点
    const relatedNode = createDeviceNode(relatedDevice, itemStatus);

    // 确保 topologyData 有 children 属性
    if (!topologyData.children) {
      topologyData.children = [];
    }

    // 确定节点关系方向
    let isReverse = isNodeReverse(relatedDevice);
    if (mainDevice.status === 1 && relatedDevice.status === 0) {
      isReverse = true;
    }

    console.log(`  节点关系方向: ${isReverse ? "反向" : "正向"}`);

    // 处理子设备关系
    let rootNodeToAdd: TopologyNode;

    if (isSubDevice) {
      console.log(`  处理子设备关系`);
      const parentNodeName = findParentNodeName(mainNode);
      console.log(`  主节点的父节点: ${parentNodeName}`);
      // 如果主节点的父节点是 "internet"，则将关联设备添加为主设备的子节点
      if (parentNodeName !== "internet" && parentNodeName !== "") {
        isReverse = false;
      }
      if (isReverse) {
        // 如果是反向关系，将主设备添加为关联设备的子节点
        rootNodeToAdd = handleSubPortRelation(mainNode, relatedNode);
      } else {
        // 如果是正向关系，将关联设备添加为主设备的子节点
        rootNodeToAdd = handleSubPortRelation(relatedNode, mainNode);
      }
    } else {
      // 处理普通设备关系
      console.log(`  处理普通设备关系`);
      if (isReverse) {
        // 如果是反向关系，将主设备添加为关联设备的子节点
        rootNodeToAdd = handleSpecialPortRelation(relatedNode, mainNode);
      } else {
        // 如果是正向关系，将关联设备添加为主设备的子节点
        rootNodeToAdd = handleNormalPortRelation(relatedNode, mainNode);
      }
    }

    // 将处理后的节点添加到拓扑图根节点
    if (rootNodeToAdd) {
      console.log(`  将节点 ${rootNodeToAdd.name} 添加到拓扑图根节点`);
      addNodeWithDedupe(rootNodeToAdd, topologyData);
    } else {
      // 如果没有返回节点，则添加主节点和关联节点
      console.log(`  未返回节点，添加主节点和关联节点到拓扑图根节点`);
      addNodeWithDedupe(mainNode, topologyData);
      addNodeWithDedupe(relatedNode, topologyData);
    }

    // 记录处理后的节点状态
    console.log(`  处理后的节点状态:`);
    console.log(`    主节点: ${mainNode.name}, 子节点数量: ${mainNode.children?.length || 0}`);
    console.log(`    关联节点: ${relatedNode.name}, 子节点数量: ${relatedNode.children?.length || 0}`);

    // 记录此设备与主设备的关系，这样其他地方可以引用到正确的端口信息
    if (relatedDevice && mainDevice && macaddr) {
      // 添加到关系映射中，保存设备与其父设备的端口关系
      if (!relatedDevice._parentRelations) {
        relatedDevice._parentRelations = [];
      }

      relatedDevice._parentRelations.push({
        parentMac: mainDevice.mac || mainDevice.macaddr,
        parentDeviceId: mainDevice.deviceId,
        sport: relatedDevice.sport,
        dport: relatedDevice.dport
      });

      console.log(`记录设备 ${relatedDevice.name || macaddr} 与父设备 ${mainDevice.deviceName || mainDevice.name} 的端口关系`);
    }
  };

  // 存储父节点映射关系
  const parentMap = new Map();
  const deviceNodesByDeviceId = new Map();
  const deviceNodesByMacAddress = new Map();

  /**
   * 构建父节点映射关系
   * 记录每个节点的父节点及其peer/subDev信息
   * @param topologyTree 拓扑树
   * @param parentNode 父节点
   * @param visited 已访问节点集合，防止循环引用
   */
  const buildParentMapAndLinkInfo = (topologyTree: any, parentNode: any = null, visited = new Set()) => {
    if (!topologyTree) return;

    // 防止循环引用 - 使用节点的唯一标识符
    const nodeKey = topologyTree.id || topologyTree.deviceId || topologyTree.name || JSON.stringify(topologyTree);
    if (visited.has(nodeKey)) {
      console.warn(`[buildParentMapAndLinkInfo] 检测到循环引用，跳过节点: ${topologyTree.name || nodeKey}`);
      return;
    }
    visited.add(nodeKey);

    // 获取当前节点和父节点的关键信息
    const deviceId = topologyTree.extra?.deviceId;
    const macaddr = (topologyTree.extra?.macaddr || topologyTree.extra?.mac || "").toLowerCase();
    const nodeName = topologyTree.name;
    const sport = topologyTree.extra?.sport;
    const dport = topologyTree.extra?.dport;
    const parentMac = parentNode ? (parentNode.extra?.macaddr || parentNode.extra?.mac || "").toLowerCase() : undefined;
    const parentName = parentNode ? parentNode.name : undefined;
    console.log(
      `[buildParentMapAndLinkInfo] 进入节点: name=${nodeName}, mac=${macaddr}, sport=${sport}, dport=${dport}, parentName=${parentName}, parentMac=${parentMac}`
    );

    // 记录节点引用
    if (deviceId) {
      deviceNodesByDeviceId.set(deviceId, topologyTree);
    }
    if (macaddr) {
      deviceNodesByMacAddress.set(macaddr, topologyTree);
    }
    // 记录父子关系
    if (parentNode) {
      const parentDeviceId = parentNode.extra?.deviceId;
      const parentMacaddr = (parentNode.extra?.macaddr || parentNode.extra?.mac || "").toLowerCase();
      if (deviceId) {
        parentMap.set(deviceId, {
          parentNode: parentNode,
          parentDeviceId: parentDeviceId,
          parentMacaddr: parentMacaddr
        });
      }
      if (macaddr) {
        parentMap.set(macaddr, {
          parentNode: parentNode,
          parentDeviceId: parentDeviceId,
          parentMacaddr: parentMacaddr
        });
      }
      // 在父节点的peer/subDev中查找当前节点的端口信息
      if (parentNode.extra?.topology) {
        const peers = parentNode.extra.topology.peer || [];
        const subDevs = parentNode.extra.topology.subDev || [];
        const allConnections = [...peers, ...subDevs];
        const matchingConnection = allConnections.find((conn: any) => {
          const connMac = (conn.macaddr || conn.mac || "").toLowerCase();
          const connDeviceId = conn.deviceId;
          return (macaddr && connMac === macaddr) || (deviceId && connDeviceId === deviceId);
        });
        if (matchingConnection) {
          console.log(
            `[buildParentMapAndLinkInfo] 找到matchingConnection: 节点name=${nodeName}, mac=${macaddr}, 赋值前 sport=${topologyTree.extra?.sport}, dport=${topologyTree.extra?.dport}`
          );
          if (!topologyTree.extra.originalSport && matchingConnection.dport) {
            topologyTree.extra.originalSport = topologyTree.extra.sport;
            topologyTree.extra.sport = matchingConnection.dport;
          }
          if (!topologyTree.extra.originalDport && matchingConnection.sport) {
            topologyTree.extra.originalDport = topologyTree.extra.dport;
            topologyTree.extra.dport = matchingConnection.sport;
          }
          console.log(
            `[buildParentMapAndLinkInfo] 赋值后: 节点name=${nodeName}, mac=${macaddr}, sport=${topologyTree.extra?.sport}, dport=${topologyTree.extra?.dport}`
          );
        } else {
          console.log(
            `[buildParentMapAndLinkInfo] 未找到matchingConnection: 节点name=${nodeName}, mac=${macaddr}, parentName=${parentName}, parentMac=${parentMac}`
          );
        }
      }
    }
    // 递归遍历子节点
    if (Array.isArray(topologyTree.children)) {
      console.log(
        `[buildParentMapAndLinkInfo] 递归子节点: name=${nodeName}, mac=${macaddr}, childrenCount=${topologyTree.children.length}`
      );
      topologyTree.children.forEach((child: any) => {
        buildParentMapAndLinkInfo(child, topologyTree, visited);
      });
    }
    // 进入HG05AC节点时，打印父节点详细信息
    if (nodeName === "HG05AC") {
      console.log("[调试] HG05AC节点，当前mac:", macaddr);
      console.log("[调试] HG05AC父节点 deviceId:", parentNode?.extra?.deviceId);
      console.log("[调试] HG05AC父节点 name:", parentNode?.name);
      console.log("[调试] HG05AC父节点 mac:", parentNode?.extra?.macaddr || parentNode?.extra?.mac);
      console.log("[调试] HG05AC父节点 peer:", JSON.stringify(parentNode?.extra?.topology?.peer));
      console.log("[调试] HG05AC父节点 subDev:", JSON.stringify(parentNode?.extra?.topology?.subDev));
    }
  };

  /**
   * 获取节点在父节点中的端口信息
   * @param node 当前节点
   * @returns 从父节点获取的端口信息 {sport, dport}
   */
  const getNodePortsFromParent = (node: any): { sport?: string; dport?: string } => {
    // 检查参数是否有效
    if (!node || !node.extra) {
      return { sport: undefined, dport: undefined };
    }

    // 如果节点已经有端口信息，直接返回
    if (node.extra.sport || node.extra.dport) {
      return { sport: node.extra.sport, dport: node.extra.dport };
    }

    // 获取节点的MAC地址和设备ID
    const nodeMac = node.extra.macaddr || node.extra.mac;
    const nodeDeviceId = node.extra.deviceId;

    if (!nodeMac && !nodeDeviceId) {
      return { sport: node.extra.sport, dport: node.extra.dport };
    }

    // 首先尝试使用拓扑图中的实际位置关系获取端口信息
    const nodeInfo = findNodeInTopology(node);
    if (nodeInfo && nodeInfo.parentNode) {
      const parentNode = nodeInfo.parentNode;

      // 在父节点的peer或subDev数组中查找当前节点
      const peers = parentNode.extra?.topology?.peer || [];
      const subDevs = parentNode.extra?.topology?.subDev || [];

      // 合并peer和subDev
      const allConnections = [...peers, ...subDevs];

      // 查找匹配的连接
      const match = allConnections.find((conn: any) => {
        const connMac = (conn.macaddr || conn.mac || "").toLowerCase();
        const connDeviceId = conn.deviceId;
        const nMac = nodeMac ? nodeMac.toLowerCase() : "";
        return (nMac && connMac === nMac) || (nodeDeviceId && connDeviceId === nodeDeviceId);
      });

      if (match) {
        // 注意：在父节点的peer/subDev数据中，
        // sport是父节点连接到子节点的端口，应该是子节点的dport
        // dport是子节点连接到父节点的端口，应该是子节点的sport
        const sport = match.dport; // 父节点的dport是子节点的sport
        const dport = match.sport; // 父节点的sport是子节点的dport

        console.log(`从父节点 ${parentNode.name} 中找到设备 ${node.name} 的端口信息: sport=${sport}, dport=${dport}`);
        return { sport, dport };
      }

      console.log(`在父节点 ${parentNode.name} 中未找到设备 ${node.name} 的端口信息，尝试使用parentMap`);
    }

    // 如果在拓扑中没有找到父子关系，尝试使用parentMap
    let key = null;
    if (nodeMac) {
      key = nodeMac.toLowerCase();
    } else if (nodeDeviceId) {
      key = nodeDeviceId;
    }

    if (key && parentMap.has(key)) {
      const parentInfo = parentMap.get(key);
      if (parentInfo && parentInfo.parentNode && parentInfo.parentNode.extra?.topology) {
        // 在父节点的peer/subDev中查找匹配项
        const peers = parentInfo.parentNode.extra.topology.peer || [];
        const subDevs = parentInfo.parentNode.extra.topology.subDev || [];
        const allConnections = [...peers, ...subDevs];

        // 查找匹配的设备
        const match = allConnections.find((conn: any) => {
          const connMac = (conn.macaddr || conn.mac || "").toLowerCase();
          const connDeviceId = conn.deviceId;
          const nMac = nodeMac ? nodeMac.toLowerCase() : "";
          return (nMac && connMac === nMac) || (nodeDeviceId && connDeviceId === nodeDeviceId);
        });

        if (match) {
          // 注意：在父节点的peer/subDev数据中，
          // sport是父节点连接到子节点的端口，应该是子节点的dport
          // dport是子节点连接到父节点的端口，应该是子节点的sport
          const sport = match.dport; // 父节点的dport是子节点的sport
          const dport = match.sport; // 父节点的sport是子节点的dport

          console.log(`从parentMap中找到设备 ${node.name} 的端口信息: sport=${sport}, dport=${dport}`);
          return { sport, dport };
        }
      }
    }

    // 如果都没有找到，则使用设备自身数据
    console.log(`未找到设备 ${node.name} 的父节点端口信息，使用设备自身数据`);
    return { sport: node.extra.sport, dport: node.extra.dport };
  };

  /**
   * 判断是否为特殊端口类型
   * @param device 设备信息
   * @returns 是否为特殊端口
   */
  // const isSpecialPortType = (device: any): boolean => {
  //   return (
  //     device.sport?.toLowerCase().startsWith("lan") ||
  //     device.sport?.toLowerCase().startsWith("ge") ||
  //     device.sport?.toLowerCase().startsWith("fe")
  //   );
  // };

  /**
   * 判断是否为RA端口类型
   * @param device 设备信息
   * @returns 是否为RA端口
   */
  // const isRaPortType = (device: any): boolean => {
  //   return device.sport?.toLowerCase() === "racli" || /^ra\d$/i.test(device.dport || "");
  // };

  /**
   * 判断节点是否需要反转
   * @param node
   */
  const isNodeReverse = (device: any): boolean => {
    return (
      device.sport?.toLowerCase().startsWith("wan") ||
      device.sport?.toLowerCase() === "racli" ||
      device.dport?.toLowerCase().startsWith("lan") ||
      // device.dport?.toLowerCase().startsWith("ge") ||
      device.dport?.toLowerCase().startsWith("fe") ||
      /^ra\d$/i.test(device.dport || "")
    );
  };

  /**
   * 处理普通端口设备关系
   * @param relatedDevice 关联设备
   * @param mainNode 主设备节点
   *
   */
  const handleNormalPortRelation = (relatedDevice: any, mainNode: any) => {
    // 检查参数是否有效
    if (!relatedDevice || !mainNode) {
      console.error("handleNormalPortRelation: relatedDevice, mainNode or mainDevice is undefined", {
        relatedDevice,
        mainNode
      });
      return;
    }

    console.log(
      `  handleNormalPortRelation: 主设备=${mainNode.name}, 关联设备=${relatedDevice.deviceName || relatedDevice.name}`
    );

    // 确保 mainNode 有 children 属性
    if (!mainNode.children) {
      mainNode.children = [];
    }

    if (mainNode.extra.status === 0 || (mainNode.extra?.status !== 0 && relatedDevice.extra?.offline !== 0)) {
      // 主设备在线或两者都离线，将关联设备添加为主设备的子节点
      console.log(`    主设备在线或两者都离线，将关联设备添加为主设备的子节点`);
      // const relatedNodeData = createDeviceNode(relatedDevice, relatedDevice.status || 0);
      addNodeWithDedupe(relatedDevice, mainNode);
      return mainNode;
    } else {
      // 否则将主设备添加为关联设备的子节点
      console.log(`    主设备离线且关联设备在线，将关联设备添加为主设备的子节点`);

      // 创建关联设备节点
      // const relatedNodeData = createDeviceNode(relatedDevice, relatedDevice.status || 0);

      // 确保关联设备节点有 children 属性
      if (!relatedDevice.children) {
        relatedDevice.children = [];
      }

      // 将主设备添加为关联设备的子节点
      addNodeWithDedupe(mainNode, relatedDevice);

      // 返回关联设备节点，以便在调用方将其添加到拓扑图根节点
      return relatedDevice;
    }

    // 返回主设备节点，以便在调用方将其添加到拓扑图根节点
    // return mainNode;
  };

  const handleSubPortRelation = (relatedNodeData: any, mainNode: any) => {
    // 检查参数是否有效
    if (!relatedNodeData || !mainNode) {
      console.error("handleSubPortRelation: relatedDevice or mainNode is undefined", { relatedNodeData, mainNode });
      return;
    }

    console.log(`  handleSubPortRelation: 主节点=${mainNode.name}, 关联节点=${relatedNodeData.name}`);

    // 确保 mainNode 有 children 属性
    if (!mainNode.children) {
      mainNode.children = [];
    }

    // 将关联设备添加为主设备的子节点
    mainNode.children.push(relatedNodeData);
    // addNodeWithDedupe(relatedNodeData, mainNode);

    // 返回主节点，以便在调用方将其添加到拓扑图根节点
    return mainNode;
  };

  /**
   * 处理特殊端口设备关系
   * @param relatedDevice 关联设备
   * @param mainNode 主设备节点
   */
  const handleSpecialPortRelation = (relatedNode: any, mainNode: any) => {
    // 检查参数是否有效
    if (!relatedNode || !mainNode) {
      console.error("handleSpecialPortRelation: relatedDevice or mainNode is undefined", { relatedNode, mainNode });
      return;
    }

    console.log(`  handleSpecialPortRelation: 主节点=${mainNode.name}, 关联节点=${relatedNode.name}`);

    // 确保关联节点有 children 属性
    if (!relatedNode.children) {
      relatedNode.children = [];
    }

    const parentNodeName = findParentNodeName(mainNode);
    console.log(`  主节点的父节点: ${parentNodeName}`);
    // 如果主节点的父节点是 "internet"，则将关联设备添加为主设备的子节点
    if (parentNodeName !== "internet" && parentNodeName !== "") {
      console.log(`    主节点的父节点是 "internet"，将关联设备添加为主设备的子节点`);
      mainNode.children.push(relatedNode);
      return mainNode;
    } else {
      // 将主设备添加为关联设备的子节点
      relatedNode.children.push(mainNode);
      // addNodeWithDedupe(mainNode, relatedNode);

      // 返回关联节点，以便在调用方将其添加到拓扑图根节点
      return relatedNode;
    }
  };

  /**
   * 切换编辑设备名称状态
   * @param row 设备数据
   */
  const toggleEditDeviceName = (row: any) => {
    console.log("toggleEditDeviceName called. row:", row);
    editName.value = !editName.value;
  };

  /**
   * 保存设备名称
   * @param row 设备数据
   * @param t 国际化函数
   */
  const handleSaveDeviceName = async (row: any, t: any) => {
    try {
      console.log("saveDeviceName called. row:", JSON.stringify(row));

      if (!drawerProps.value.row.deviceId) {
        ElMessage.error({ message: t("topology.deviceIdRequired") });
        return;
      }

      const response = await saveDeviceName(drawerProps.value.row.deviceId, row.name, row.extra.macaddr);

      console.log("API 响应数据:", response);

      if (!response || response.code !== "200") {
        ElMessage.error({ message: response.msg || t("topology.operationFailed") });
        return;
      }

      ElMessage.success({ message: t("topology.operationSuccess") });
      editName.value = false;
      deviceNameChanged.value = true;
    } catch (error) {
      console.error("保存设备名称失败:", error);
      ElMessage.error({ message: t("topology.operationFailed") });
    }
  };

  /**
   * 删除网桥客户端
   * @param row 设备数据
   * @param t 国际化函数
   */
  const handleDeleteBridgeClient = async (row: any, t: any) => {
    try {
      console.log("deleteBridgeClient called. row:", JSON.stringify(row));

      if (!drawerProps.value.row.deviceId) {
        ElMessage.error({ message: t("topology.deviceIdRequired") });
        return;
      }

      const response = await deleteBridgeClient(drawerProps.value.row.deviceId, row.extra.macaddr);

      console.log("API 响应数据:", response);

      if (!response || response.code !== "200") {
        ElMessage.error({ message: response.msg || t("topology.operationFailed") });
        return;
      }

      ElMessage.success({ message: t("topology.operationSuccess") });
      bridgeClientDrawerVisible.value = false;
    } catch (error) {
      console.error("删除网桥客户端失败:", error);
      ElMessage.error({ message: t("topology.operationFailed") });
    }
  };

  /**
   * 加载拓扑数据
   * @param deviceId 设备ID
   * @param t 国际化函数
   */
  const loadTopologyData = async (deviceId: string, t: any) => {
    try {
      if (!deviceId) {
        console.warn(t("topology.noDeviceSelected"));
        return null;
      }

      const response = await getTopologyData(deviceId);

      // if (!response || response.code !== "200") {
      //   ElMessage.error({ message: response.msg || t("topology.loadFailed") });
      //   return null;
      // }

      // return response.data?.system?.topology || [];
      return response;
    } catch (error) {
      console.error("加载拓扑数据失败:", error);
      ElMessage.error({ message: t("topology.loadFailed") });
      return null;
    }
  };

  const loadOfflineTopologyData = async (deviceId: string, t: any) => {
    if (!deviceId) {
      console.warn(t("topology.noDeviceSelected"));
      return null;
    }

    const response = await getDeviceTopology({ deviceId: deviceId });

    if (!response || response.code !== "200") {
      ElMessage.error({ message: response.msg || t("topology.loadFailed") });
      return null;
    }
  };

  /**
   * 输出节点层级结构
   * @param node 节点
   * @param level 层级
   */
  const logNodeStructure = (node: any, level: number, visited = new Set()) => {
    if (!node) return;
    if (visited.has(node)) return;
    visited.add(node);
    const indent = "  ".repeat(level);
    console.log(
      `${indent}${level === 0 ? "\u2514\u2500" : "\u251c\u2500"} ${node.name} ${node.children?.length ? `(子节点: ${node.children.length})` : "(叶节点)"}`
    );
    if (node.children && node.children.length > 0) {
      node.children.forEach((child: any) => {
        logNodeStructure(child, level + 1, visited);
      });
    }
  };

  /**
   * 检查节点是否可以设置为出口设备
   * @param node 要检查的节点
   * @returns 是否可以设置为出口设备
   */
  const canBeSetAsExitDevice = (node: any): boolean => {
    // 检查节点是否有效
    if (!node || !node.extra) return false;

    // 获取端口类型
    const sport = node.extra.sport?.toLowerCase() || "";
    const dport = node.extra.dport?.toLowerCase() || "";

    // 检查端口是否为空
    if (!sport || !dport) {
      console.log(`节点 ${node.name} 的端口信息不完整，sport=${sport}, dport=${dport}`);
      return false;
    }
    // 检查端口类型是否相同
    const sportType = getPortType(sport);
    const dportType = getPortType(dport);

    // 同类型端口可以设为出口设备
    return sportType === dportType;
  };

  /**
   * 获取端口类型，分为LAN、WAN、RA三大类
   * @param port 端口名称
   * @returns 端口类型
   */
  const getPortType = (port: string): string => {
    if (!port) return "";

    const portLower = port.toLowerCase();

    // WAN类型端口
    if (portLower.startsWith("wan")) {
      return "WAN";
    }

    // RA类型端口：RAx（如RA2/RA5/36）和RACLI
    if (portLower === "racli" || /^ra\d+/i.test(port)) {
      return "RA";
    }

    // LAN类型端口，包括lan、ge、port、fe等
    if (portLower.startsWith("lan") || portLower.startsWith("ge") || portLower.startsWith("port") || portLower.startsWith("fe")) {
      return "LAN";
    }

    // 默认为LAN类型（其它未指定端口除了WAN/RA都算LAN）
    return "LAN";
  };

  /**
   * 递归检查节点是否可以设为出口设备
   * 规则：
   * 1. 一级节点不能设为出口设备(拓扑图中二级节点作为实际的一级节点)
   * 2. 二级及以下节点需要满足sport和dport同类型才能设为出口设备
   * 3. 如果父节点不能设为出口设备，所有子节点都不能设为出口设备
   * @param node 要检查的节点
   * @param parentCanBeExit 父节点是否可以设为出口设备
   * @param level 节点层级
   * @returns 是否可以设为出口设备
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const checkNodeExitEligibility = (
    node: any,
    parentCanBeExit: boolean = true,
    level: number = 0,
    visited = new Set()
  ): boolean => {
    // 防止循环引用
    const nodeKey = node.id || node.deviceId || node.name || JSON.stringify(node);
    if (visited.has(nodeKey)) {
      console.warn(`[checkNodeExitEligibility] 检测到循环引用，跳过节点: ${node.name || nodeKey}`);
      return false;
    }
    visited.add(nodeKey);

    // 一级节点(根节点)不能设为出口设备
    if (level <= 1) return false;

    // 如果父节点不能设为出口设备，子节点也不能
    if (!parentCanBeExit) return false;

    // 根据端口类型判断当前节点是否可以设为出口设备
    const currentNodeCanBeExit = canBeSetAsExitDevice(node);

    // 如果节点有子节点，递归检查
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        // 递归检查子节点，传递当前节点的出口设备资格
        checkNodeExitEligibility(child, currentNodeCanBeExit, level + 1, visited);
      }
    }

    return currentNodeCanBeExit;
  };

  /**
   * 加载出口设备信息
   * @param groupId 分组ID
   * @returns 出口设备列表
   */
  const loadExitDevices = async (groupId: string | number) => {
    try {
      // 清空之前的出口设备集合
      exitDeviceSet.clear();

      // 查询分组的出口设备
      const response = await getExitByGroupId(groupId);

      if (response && response.code === "200" && response.data) {
        // 将出口设备ID添加到集合中，统一用大写
        response.data.forEach(item => {
          const mac = (item.exitDeviceId || "").toUpperCase();
          if (mac) {
            exitDeviceSet.add(mac);
          }
        });

        console.log(`加载到 ${exitDeviceSet.size} 个出口设备信息`);
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("加载出口设备信息失败:", error);
      return [];
    }
  };

  /**
   * 设置出口设备
   * @param groupId 分组ID
   * @param deviceInfo 设备信息
   * @param t 国际化函数
   * @returns 是否设置成功
   */
  const handleSetAsExitDevice = async (groupId: string | number, deviceInfo: any, t: any): Promise<boolean> => {
    try {
      // 统一使用mac或macaddr作为出口设备ID（大写）
      const exitDeviceId = (
        deviceInfo.extra?.macaddr ||
        deviceInfo.extra?.mac ||
        deviceInfo.macaddr ||
        deviceInfo.mac ||
        ""
      ).toUpperCase();

      if (!exitDeviceId) {
        ElMessage.error(t("topology.deviceIdRequired"));
        return false;
      }

      // 检查设备是否可以设为出口设备
      if (!canBeSetAsExitDevice(deviceInfo)) {
        ElMessage.error(t("device.cannotSetAsExitDevice"));
        return false;
      }

      // 准备请求参数
      const params = [
        {
          groupId,
          exitDeviceId
        }
      ];

      // 调用API设置出口设备
      const response = await saveGroupExit(params);

      if (response && response.code === "200") {
        // 更新出口设备集合（大写）
        exitDeviceSet.add(exitDeviceId);

        // 返回成功消息，让调用方刷新拓扑图
        ElMessage.success(t("device.setAsExitDeviceSuccess"));
        return true;
      } else {
        ElMessage.error(response?.msg || t("topology.operationFailed"));
        return false;
      }
    } catch (error) {
      console.error("设置出口设备失败:", error);
      ElMessage.error(t("topology.operationFailed"));
      return false;
    }
  };

  /**
   * 取消出口设备
   * @param deviceInfo 设备信息
   * @param t 国际化函数
   * @returns 是否取消成功
   */
  const handleCancelExitDevice = async (deviceInfo: any, t: any): Promise<boolean> => {
    try {
      // 统一使用mac或macaddr作为出口设备ID（大写）
      const exitDeviceId = (
        deviceInfo.extra?.macaddr ||
        deviceInfo.extra?.mac ||
        deviceInfo.macaddr ||
        deviceInfo.mac ||
        ""
      ).toUpperCase();

      if (!exitDeviceId) {
        ElMessage.error(t("topology.deviceIdRequired"));
        return false;
      }

      // 调用API删除出口设备
      const response = await deleteExitByDeviceId({ exitDeviceId });

      if (response && response.code === "200") {
        // 从出口设备集合中移除（大写）
        exitDeviceSet.delete(exitDeviceId);

        // 返回成功消息，让调用方刷新拓扑图
        ElMessage.success(t("device.cancelExitDeviceSuccess"));
        return true;
      } else {
        ElMessage.error(response?.msg || t("topology.operationFailed"));
        return false;
      }
    } catch (error) {
      console.error("取消出口设备失败:", error);
      ElMessage.error(t("topology.operationFailed"));
      return false;
    }
  };

  /**
   * 处理出口设备，重组拓扑结构
   * @param exitDevices 出口设备列表
   */
  const processExitDevices = (exitDevices: any[]) => {
    if (!exitDevices || !exitDevices.length) return;

    // 首先清除所有节点的出口设备标记
    const clearExitDeviceFlags = (node: any) => {
      if (!node) return;

      // 清除当前节点的出口设备标记
      if (node.isExitDevice) {
        node.isExitDevice = false;
      }

      // 递归清除子节点的出口设备标记
      if (node.children && node.children.length > 0) {
        node.children.forEach((child: any) => clearExitDeviceFlags(child));
      }
    };

    // 清除所有节点的出口设备标记
    clearExitDeviceFlags(topologyData);

    // 遍历所有出口设备
    exitDevices.forEach(exitDevice => {
      // 统一用大写
      const exitDeviceId = (exitDevice.exitDeviceId || "").toUpperCase();
      if (!exitDeviceId) return;

      // 在拓扑图中查找出口设备节点
      let foundExitNode = null;

      // 递归查找函数
      const findExitNode = (node: any): any => {
        if (!node) return null;

        // 检查当前节点是否是出口设备，忽略大小写
        const nodeMac = (node.extra?.macaddr || node.extra?.mac || "").toUpperCase();
        const nodeId = (node.extra?.deviceId || "").toUpperCase();

        // 使用MAC地址或设备ID进行匹配
        if (nodeMac === exitDeviceId || nodeId === exitDeviceId) {
          console.log(`找到出口设备节点:`, {
            name: node.name,
            mac: nodeMac,
            deviceId: nodeId,
            match: nodeMac === exitDeviceId ? "MAC匹配" : "ID匹配"
          });
          return node;
        }

        // 递归查找子节点
        if (node.children && node.children.length > 0) {
          for (const child of node.children) {
            const result = findExitNode(child);
            if (result) return result;
          }
        }

        return null;
      };

      // 从拓扑图中查找出口设备
      foundExitNode = findExitNode(topologyData);

      if (foundExitNode) {
        // 标记该节点为出口设备
        foundExitNode.isExitDevice = true;
        console.log(`标记节点 ${foundExitNode.name} 为出口设备`);

        // 获取出口设备在拓扑图中的位置信息
        const nodeInfo = findNodeInTopology(foundExitNode);

        // 如果找到了出口设备节点的信息
        if (nodeInfo && nodeInfo.parentNode) {
          const parentNode = nodeInfo.parentNode;

          // 输出节点信息进行调试
          console.log(`出口设备节点信息:`, {
            name: foundExitNode.name,
            isDirectChild: nodeInfo.isDirectChild,
            level: nodeInfo.level,
            parentNode: parentNode.name,
            path: nodeInfo.path.map(n => n.name).join(" -> ")
          });

          // 如果出口设备不是一级节点（即parentNode不是internet），则需要进行路径反转
          if (parentNode.name !== "internet") {
            console.log(`出口设备 ${foundExitNode.name} 不是一级节点，开始路径反转`);

            // 实现路径反转逻辑
            performPathReversal(foundExitNode, nodeInfo);
          } else {
            console.log(`出口设备 ${foundExitNode.name} 已经是顶级节点，无需提升`);
          }
        }
      }
    });

    // 输出处理后的拓扑图结构
    console.log("\n处理后的拓扑图结构:");
    logNodeStructure(topologyData, 0);
  };

  /**
   * 处理重复MAC地址的节点，按照以下规则合并信息：
   * 1. 节点排序的其他基本规则不变
   * 2. 如果后出现节点的信息更完整，则用后面节点的信息补齐前面节点
   * 3. 如果后面出现的节点还有子节点，则将子节点整合到前面的节点中
   */
  const mergeDuplicateMacNodes = () => {
    // 存储已经发现的MAC地址节点
    const macAddressMap = new Map();
    // 辅助数组存储需要处理的重复节点信息
    const duplicateNodes = [];

    // 递归逆序遍历拓扑图，收集所有节点的MAC地址和位置信息
    const collectAllNodes = (node, path = [], parentNode = null, visited = new Set()) => {
      if (!node) return;

      // 防止无限递归 - 使用节点的唯一标识符
      const nodeKey = node.id || node.deviceId || node.name || JSON.stringify(node);
      if (visited.has(nodeKey)) {
        console.warn(`检测到循环引用，跳过节点: ${node.name || nodeKey}`);
        return;
      }
      visited.add(nodeKey);

      const mac = node.extra?.macaddr || node.extra?.mac;
      if (mac) {
        const macLower = mac.toLowerCase();

        // 如果已经有这个MAC地址，记录重复信息
        if (macAddressMap.has(macLower)) {
          duplicateNodes.push({
            existingNode: macAddressMap.get(macLower).node,
            existingPath: macAddressMap.get(macLower).path,
            existingParent: macAddressMap.get(macLower).parent,
            duplicateNode: node,
            duplicatePath: [...path],
            duplicateParent: parentNode
          });

          if (mac.toUpperCase() === "90-E2-FF-00-08-0D") {
            console.log(`[特殊MAC合并] 发现重复MAC地址 ${mac}:`, {
              existingNode: macAddressMap.get(macLower).node.name,
              duplicateNode: node.name,
              existingPath: macAddressMap
                .get(macLower)
                .path.map(n => n.name)
                .join(" -> "),
              duplicatePath: [...path].map(n => n.name).join(" -> ")
            });
          }
        } else {
          // 记录节点信息
          macAddressMap.set(macLower, {
            node,
            path: [...path],
            parent: parentNode
          });
        }
      }

      // 递归遍历子节点
      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          collectAllNodes(child, [...path, node], node, visited);
        }
      }
    };

    // 合并节点信息
    const mergeNodeInfo = (existingNode, duplicateNode, visited = new Set()) => {
      // 防止循环引用
      const nodeKey = existingNode.id || existingNode.deviceId || existingNode.name || JSON.stringify(existingNode);
      if (visited.has(nodeKey)) {
        console.warn(`[mergeNodeInfo] 检测到循环引用，跳过节点: ${existingNode.name || nodeKey}`);
        return;
      }
      visited.add(nodeKey);
      // 更新节点名称，如果原来节点为空或为"未知"，但新节点有名称
      if (
        (!existingNode.name || existingNode.name === t("topology.unknown") || existingNode.name === "未知") &&
        duplicateNode.name
      ) {
        existingNode.name = duplicateNode.name;
      }

      // 更新extra信息，保留原有信息，补充缺失的信息
      if (existingNode.extra && duplicateNode.extra) {
        // 针对特定字段进行补充
        if (!existingNode.extra.deviceName && duplicateNode.extra.deviceName) {
          existingNode.extra.deviceName = duplicateNode.extra.deviceName;
        }

        if (!existingNode.extra.deviceId && duplicateNode.extra.deviceId) {
          existingNode.extra.deviceId = duplicateNode.extra.deviceId;
        }

        if (!existingNode.extra.deviceModel && duplicateNode.extra.deviceModel) {
          existingNode.extra.deviceModel = duplicateNode.extra.deviceModel;
        }

        // 合并system.topology信息
        if (duplicateNode.extra.system && duplicateNode.extra.system.topology) {
          if (!existingNode.extra.system) {
            existingNode.extra.system = {};
          }
          if (!existingNode.extra.system.topology) {
            existingNode.extra.system.topology = {};
          }

          // 合并peer设备
          if (duplicateNode.extra.system.topology.peer) {
            if (!existingNode.extra.system.topology.peer) {
              existingNode.extra.system.topology.peer = [];
            }
            // 合并并去重
            const existingPeers = existingNode.extra.system.topology.peer;
            duplicateNode.extra.system.topology.peer.forEach(peer => {
              if (!existingPeers.some(p => (p.macaddr || p.mac) === (peer.macaddr || peer.mac))) {
                existingPeers.push(peer);
              }
            });
          }

          // 合并subDev设备
          if (duplicateNode.extra.system.topology.subDev) {
            if (!existingNode.extra.system.topology.subDev) {
              existingNode.extra.system.topology.subDev = [];
            }
            // 合并并去重
            const existingSubDevs = existingNode.extra.system.topology.subDev;
            duplicateNode.extra.system.topology.subDev.forEach(subDev => {
              if (!existingSubDevs.some(s => (s.macaddr || s.mac) === (subDev.macaddr || subDev.mac))) {
                existingSubDevs.push(subDev);
              }
            });
          }
        }
      }

      // 合并子节点
      if (duplicateNode.children && duplicateNode.children.length > 0) {
        if (!existingNode.children) {
          existingNode.children = [];
        }

        // 将重复节点的子节点添加到现有节点
        for (const childNode of duplicateNode.children) {
          // 检查是否已经存在相同的子节点
          const existingChildIndex = existingNode.children.findIndex(existing => isSameNode(existing, childNode));

          if (existingChildIndex !== -1) {
            // 如果已存在相同的子节点，递归合并
            mergeNodeInfo(existingNode.children[existingChildIndex], childNode, visited);
          } else {
            // 如果不存在相同的子节点，添加新子节点
            existingNode.children.push(childNode);
          }
        }
      }
      // 合并所有链路并调整父子关系
      mergeAndRebuildLinks(existingNode, duplicateNode);
    };

    // 从重复节点的父节点中移除该节点
    const removeNodeFromParent = (parent, node) => {
      if (!parent || !parent.children) return false;

      const index = parent.children.findIndex(child => child === node);
      if (index !== -1) {
        parent.children.splice(index, 1);
        return true;
      }
      return false;
    };

    // 从根节点开始遍历收集所有节点
    collectAllNodes(topologyData, [], null, new Set());

    // 处理所有重复节点
    for (const dupInfo of duplicateNodes) {
      const { existingNode, duplicateNode, duplicateParent } = dupInfo;

      // 在日志中输出重复节点的信息
      const existingMac = existingNode.extra?.macaddr || existingNode.extra?.mac;
      if (existingMac && existingMac.toUpperCase() === "90-E2-FF-00-08-0D") {
        console.log(`[特殊MAC合并] 合并节点:`, {
          existingNode: existingNode.name,
          duplicateNode: duplicateNode.name
        });
      }

      // 1. 合并节点信息
      mergeNodeInfo(existingNode, duplicateNode);

      // 2. 从父节点中移除重复节点
      if (duplicateParent) {
        removeNodeFromParent(duplicateParent, duplicateNode);
      }
    }

    // 最后打印处理结果
    console.log(`处理完成，合并了 ${duplicateNodes.length} 个重复MAC地址的节点`);
  };

  /**
   * 清空所有设备MAC地址集合
   */
  const clearAllDeviceMacAddresses = () => {
    allDeviceMacAddresses.clear();
    deviceNodesByMac.clear();
    peerMacAddresses.clear();
    parentMap.clear();
    deviceNodesByDeviceId.clear();
    deviceNodesByMacAddress.clear();
  };

  /**
   * 执行路径反转操作 - 将出口设备提升为一级节点，严格路径反转
   * @param exitNode 出口设备节点
   * @param nodeInfo 节点信息
   */
  const performPathReversal = (exitNode: any, nodeInfo: any) => {
    console.log(`开始执行出口设备路径反转操作: ${exitNode.name}`);

    // 只处理非一级节点
    if (nodeInfo.level <= 1) {
      console.log(`节点 ${exitNode.name} 已经是一级节点，无需反转`);
      return;
    }

    // 获取路径 [internet, A, B, D]
    const path = nodeInfo.path;
    if (path.length < 3) return; // 已经是一级节点，无需提升

    // 记录路径上每个节点原有的children（去掉路径下一个节点）
    const oldChildren = path.map((node, idx) => {
      const next = path[idx + 1];
      return node.children ? node.children.filter(child => child !== next) : [];
    });

    // 反转父子关系
    // 从出口设备（最后一个）往前，依次把前一个节点作为当前节点的唯一子节点
    for (let i = path.length - 1; i > 1; i--) {
      path[i].children = [path[i - 1], ...oldChildren[i]];
    }
    // path[1]（原一级节点）children 只保留原有非路径子节点
    path[1].children = oldChildren[1];

    // internet.children 用出口设备替换原一级节点
    const idx = path[0].children.findIndex(child => child === path[1]);
    if (idx !== -1) {
      path[0].children[idx] = path[path.length - 1];
    }
  };

  // 设备类型优先级，数值越小优先级越高
  function getDevicePriority(type: string): number {
    if (!type) return 99;
    if (type === "route" || type === "ac") return 1;
    if (type === "switch" || type === "reaper") return 2;
    if (type === "ap" || type === "bridge") return 3;
    return 99;
  }

  // 端口优先级，数值越小优先级越高
  function getPortPriority(port: string): number {
    if (!port) return 99;
    const portLower = port.toLowerCase();

    // RAx 格式：RA1, RA2, RA5/36 等，优先级最高
    if (/^ra\d+/i.test(port)) return 1;

    // RACLI 格式，优先级次高
    if (portLower === "racli") return 2;

    // LAN 类型：LANx、GEx、portx、FEx、其它未指定端口（除了WAN/RA都算LAN）
    if (portLower.startsWith("lan") || portLower.startsWith("ge") || portLower.startsWith("port") || portLower.startsWith("fe"))
      return 3;

    // WAN 类型：WAN、WANx，优先级最低
    if (portLower.startsWith("wan")) return 4;

    // 其他未指定端口默认为LAN类型
    return 3;
  }

  // 比较链路优先级，先比端口，再比设备类型
  function compareLinkPriority(linkA: any, linkB: any): number {
    const portPriA = getPortPriority(linkA.sport);
    const portPriB = getPortPriority(linkB.sport);

    // 添加调试日志
    if (linkA.macaddr === "90-E2-FC-02-B4-15" || linkB.macaddr === "90-E2-FC-02-B4-15") {
      console.log(`[优先级比较] MAC: 90-E2-FC-02-B4-15`);
      console.log(`  LinkA: sport=${linkA.sport}(优先级${portPriA}), deviceType=${linkA.deviceType}`);
      console.log(`  LinkB: sport=${linkB.sport}(优先级${portPriB}), deviceType=${linkB.deviceType}`);
    }

    if (portPriA !== portPriB) return portPriA - portPriB;
    const devPriA = getDevicePriority(linkA.deviceType);
    const devPriB = getDevicePriority(linkB.deviceType);
    return devPriA - devPriB;
  }

  // 合并节点时递归转移所有链路，并根据优先级调整唯一父子关系
  function mergeAndRebuildLinks(existingNode: any, duplicateNode: any, visited = new Set()) {
    if (visited.has(existingNode)) return;
    visited.add(existingNode);
    if (duplicateNode.children && duplicateNode.children.length > 0) {
      for (const childNode of duplicateNode.children) {
        if (isAncestor(existingNode, childNode)) {
          console.warn(`检测到环，禁止将祖先节点 ${existingNode.name} 挂载为 ${childNode.name} 的子节点`);
          continue;
        }
        const existingChildIndex = existingNode.children.findIndex(item => isSameNode(item, childNode));
        if (existingChildIndex !== -1) {
          mergeAndRebuildLinks(existingNode.children[existingChildIndex], childNode, visited);
        } else {
          existingNode.children.push(childNode);
        }
      }
    }
    // 递归合并 peer/subDev 链路，并根据优先级调整唯一父子关系
    const duplicatePeers = duplicateNode.extra?.system?.topology?.peer || [];
    const duplicateSubDevs = duplicateNode.extra?.system?.topology?.subDev || [];
    const allLinks = [...duplicatePeers, ...duplicateSubDevs];
    // 记录所有指向同一节点的父链路
    const nodeLinkMap = new Map(); // mac -> [link, parentNode]
    for (const link of allLinks) {
      const targetMac = link.macaddr || link.mac;
      if (!targetMac) continue;
      const targetNode = deviceNodesByMac && deviceNodesByMac.get ? deviceNodesByMac.get(targetMac.toLowerCase()) : null;
      if (!targetNode) continue;
      if (!nodeLinkMap.has(targetMac)) nodeLinkMap.set(targetMac, []);
      nodeLinkMap.get(targetMac).push({ link, parentNode: existingNode });
    }
    // 对每个节点，选出优先级最高的父节点
    for (const [mac, links] of nodeLinkMap.entries()) {
      let best = links[0];
      for (let i = 1; i < links.length; i++) {
        if (compareLinkPriority(links[i].link, best.link) < 0) {
          best = links[i];
        }
      }
      // 只在优先级最高的父节点下挂载
      const targetNode = deviceNodesByMac.get(mac.toLowerCase());
      if (targetNode && best.parentNode) {
        // 主树唯一挂载
        if (!best.parentNode.children.some(item => isSameNode(item, targetNode))) {
          best.parentNode.children.push(targetNode);
        }
      }
      // 其它父链路信息保留在 targetNode.extra._allParentLinks
      if (targetNode) {
        if (!targetNode.extra._allParentLinks) targetNode.extra._allParentLinks = [];
        for (const l of links) {
          targetNode.extra._allParentLinks.push({
            parentMac: l.parentNode.extra?.macaddr || l.parentNode.extra?.mac,
            parentDeviceId: l.parentNode.extra?.deviceId,
            link: l.link
          });
        }
      }
    }
  }

  // 修正所有节点的主父节点唯一挂载，严格按 compareLinkPriority 选主父节点
  function fixAllNodeParentByPriority(topologyData) {
    const macNodeMap = new Map();
    function collectAllNodes(node, visited) {
      if (!node) return;

      // 使用更可靠的节点标识符来防止循环
      const nodeKey = node.id || node.deviceId || node.name || JSON.stringify(node);
      if (visited.has(nodeKey)) return;
      visited.add(nodeKey);

      const mac = node.extra?.macaddr || node.extra?.mac;
      if (mac) macNodeMap.set(mac.toLowerCase(), node);
      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          collectAllNodes(child, visited);
        }
      }
    }
    const visitedNodes = new Set();
    collectAllNodes(topologyData, visitedNodes);
    const allParentLinksMap = new Map();
    function collectAllParentLinks(node, visited) {
      if (!node) return;

      // 使用更可靠的节点标识符来防止循环
      const nodeKey = node.id || node.deviceId || node.name || JSON.stringify(node);
      if (visited.has(nodeKey)) return;
      visited.add(nodeKey);

      const peers = node.extra?.system?.topology?.peer || [];
      const subDevs = node.extra?.system?.topology?.subDev || [];
      const allLinks = [...peers, ...subDevs];
      for (const link of allLinks) {
        const targetMac = link.macaddr || link.mac;
        if (!targetMac) continue;
        if (!allParentLinksMap.has(targetMac)) allParentLinksMap.set(targetMac, []);
        allParentLinksMap.get(targetMac).push({ link, parentNode: node });
      }
      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          collectAllParentLinks(child, visited);
        }
      }
    }
    const visitedParentLinks = new Set();
    collectAllParentLinks(topologyData, visitedParentLinks);
    for (const [mac, links] of allParentLinksMap.entries()) {
      // 添加调试日志
      if (mac === "90-e2-fc-02-b4-15") {
        console.log(`[fixAllNodeParentByPriority] 处理MAC: ${mac}, 链路数量: ${links.length}`);
        links.forEach((link, index) => {
          console.log(`  链路${index}: sport=${link.link.sport}, dport=${link.link.dport}, parentNode=${link.parentNode.name}`);
        });
      }

      let best = links[0];
      for (let i = 1; i < links.length; i++) {
        if (compareLinkPriority(links[i].link, best.link) < 0) {
          best = links[i];
        }
      }

      // 添加调试日志
      if (mac === "90-e2-fc-02-b4-15") {
        console.log(`  选择的最佳链路: sport=${best.link.sport}, dport=${best.link.dport}, parentNode=${best.parentNode.name}`);
      }

      const targetNode = macNodeMap.get(mac.toLowerCase());
      if (targetNode && best.parentNode) {
        for (const l of links) {
          if (l.parentNode.children) {
            l.parentNode.children = l.parentNode.children.filter(child => !isSameNode(child, targetNode));
          }
        }
        if (!isAncestor(targetNode, best.parentNode) && !best.parentNode.children.some(item => isSameNode(item, targetNode))) {
          best.parentNode.children.push(targetNode);
        }
        if (!targetNode.extra._allParentLinks) targetNode.extra._allParentLinks = [];
        for (const l of links) {
          targetNode.extra._allParentLinks.push({
            parentMac: l.parentNode.extra?.macaddr || l.parentNode.extra?.mac,
            parentDeviceId: l.parentNode.extra?.deviceId,
            link: l.link
          });
        }
      }
    }
  }

  return {
    topologyData,
    drawerVisible,
    drawerProps,
    clickNodeProps,
    editName,
    deviceNameChanged,
    bridgeClientDrawerVisible,
    generateData,
    toggleEditDeviceName,
    handleSaveDeviceName,
    handleDeleteBridgeClient,
    loadTopologyData,
    loadOfflineTopologyData,
    clearAllDeviceMacAddresses,
    loadExitDevices,
    handleSetAsExitDevice,
    handleCancelExitDevice,
    processExitDevices,
    getNodePortsFromParent
  };
}
