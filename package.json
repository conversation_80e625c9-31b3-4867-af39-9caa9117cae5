{"name": "geeker-admin", "private": true, "version": "1.2.0", "type": "module", "description": "geeker-admin open source management system", "author": {"name": "<PERSON>ker", "email": "<EMAIL>", "url": "https://github.com/HalseySpicy"}, "license": "MIT", "homepage": "https://github.com/HalseySpicy/Geeker-Admin", "repository": {"type": "git", "url": "**************:HalseySpicy/Geeker-Admin.git"}, "bugs": {"url": "https://github.com/HalseySpicy/Geeker-Admin/issues"}, "scripts": {"dev": "vite", "serve": "vite", "build:dev": "vue-tsc && vite build --mode development", "build:dev:skip-ts": "vite build --mode development", "build:test": "vue-tsc && vite build --mode test", "build:test:skip-ts": "vite build --mode test", "build:pro": "vue-tsc && vite build --mode production", "build:pro:skip-ts": "vite build --mode production", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "pnpm build:dev && vite preview", "lint": "npm run lint:eslint && npm run lint:prettier && npm run lint:stylelint", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "prepare": "husky install", "release": "standard-version", "commit": "git add -A && czg && git push"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.11.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "1.8.2", "d3": "^7.9.0", "dayjs": "^1.11.13", "driver.js": "^1.3.6", "echarts": "^5.5.1", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.8.3", "geeker-admin": "file:", "jose": "^6.0.10", "lodash": "^4.17.21", "md5": "^2.3.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.2.2", "pinia-plugin-persistedstate": "^3.2.3", "qs": "^6.13.0", "screenfull": "^6.0.2", "sortablejs": "^1.15.3", "vue": "^3.5.5", "vue-i18n": "^9.14.0", "vue-router": "^4.4.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@types/intro.js": "^5.1.5", "@types/lodash": "^4.17.16", "@types/md5": "^2.3.5", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.16", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-vue": "^5.1.3", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.20", "code-inspector-plugin": "^0.16.1", "cz-git": "1.9.2", "czg": "^1.9.4", "esbuild-register": "^3.6.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.28.0", "husky": "^8.0.3", "intro.js": "^7.2.0", "lint-staged": "^15.2.10", "postcss": "^8.4.45", "postcss-html": "^1.7.0", "prettier": "^3.3.3", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.78.0", "standard-version": "^9.5.0", "stylelint": "^16.9.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^5.1.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-scss": "^13.1.0", "typescript": "^5.6.2", "unplugin-vue-setup-extend-plus": "^1.0.1", "vite": "^5.4.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.2", "vite-plugin-pwa": "^0.20.5", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.4.5", "vue-tsc": "^2.1.6"}, "engines": {"node": ">=16.18.0"}, "browserslist": {"production": ["> 1%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "packageManager": "pnpm@9.14.1+sha1.d6e4ad498851766dc231405c14592ac6dd305ab1"}